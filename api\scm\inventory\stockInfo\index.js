import request from "../../../../utils/request";

/**
 * 库存信息相关API
 */

// 获取库存信息分页列表
export function getStockInfoPageApi(params) {
  return request({
    url: '/scm/inventory/stock-info/page',
    method: 'GET',
    params,
  })
}

// 获取库存信息详情
export function getStockInfoApi(id) {
  return request({
    url: `/scm/inventory/stock-info/get?id=${id}`,
    method: 'GET',
  })
}

// 更新库存信息
export function updateStockInfoApi(data) {
  return request({
    url: '/scm/inventory/stock-info/update',
    method: 'PUT',
    data,
  })
}

// 导出库存信息Excel
export function exportStockInfoApi(params) {
  return request({
    url: '/scm/inventory/stock-info/export-excel',
    method: 'GET',
    params,
  })
}

// 获取库存交易明细列表
export function getTransactionListByInventoryIdApi(inventoryId) {
  return request({
    url: `/scm/inventory/stock-info/transaction/list-by-inventory-id?inventoryId=${inventoryId}`,
    method: 'GET',
  })
}

// 获取单位列表
export function getUnitListApi(params) {
  return request({
    url: '/scm/base/unit/page',
    method: 'GET',
    params,
  })
}

// 获取仓库列表
export function getWarehouseListApi(params) {
  return request({
    url: '/scm/inventory/warehouse/list',
    method: 'GET',
    params,
  })
}

// 统一的API对象
export const stockInfoApi = {
  getStockInfo: getStockInfoApi,
  updateStockInfo: updateStockInfoApi,
  getTransactionListByInventoryId: getTransactionListByInventoryIdApi,
  getStockInfoPage: getStockInfoPageApi,
  exportStockInfo: exportStockInfoApi,
  getUnitList: getUnitListApi,
  getWarehouseList: getWarehouseListApi
}