<template>
	<view class="edit-form-detail-page">

		<!-- 表单内容 -->
		<view class="form-container">
			<uv-form
				ref="formRef"
				:model="formDetail"
				:rules="formRules"
				label-position="top"
				label-width="140"
			>
				<!-- 序号 -->
				<uv-form-item label="序号" prop="num">
					<uv-input
						v-model="formDetail.num"
						placeholder="请输入序号"
						type="number"
					/>
				</uv-form-item>

				<!-- 单号 -->
				<uv-form-item label="单号" prop="bizOrderNo">
					<uv-input
						v-model="formDetail.bizOrderNo"
						placeholder="请输入单号"
					/>
				</uv-form-item>

				<!-- 库位 -->
				<uv-form-item label="库位" prop="locationId">
					<select-picker
						:options="locationOptions"
						:value="getSelectedLocation()"
						:title="'选择库位'"
						:labelField="'name'"
						:valueField="'id'"
						:placeholder="'请选择库位'"
						@input="handleLocationChange"
					/>
				</uv-form-item>

				<!-- 物料 -->
				<uv-form-item label="物料" prop="materialId">
					<select-picker
						:options="materialOptions"
						:value="getSelectedMaterial()"
						:title="'选择物料'"
						:labelField="'name'"
						:valueField="'id'"
						:placeholder="'请选择物料'"
						:enableLoadMore="true"
						:pageSize="10"
						@input="handleMaterialChange"
						@loadMore="loadMoreMaterials"
						@search="searchMaterials"
						@resetSearch="resetMaterialSearch"
					/>
				</uv-form-item>

				<!-- 单位 -->
				<uv-form-item label="单位" prop="unit">
					<uv-input
						v-model="formDetail.unitName"
						placeholder="单位"
						disabled
					/>
				</uv-form-item>

				<!-- 单价 -->
				<uv-form-item label="单价" prop="unitPrice">
					<uv-input
						v-model="formDetail.unitPrice"
						placeholder="请输入单价"
						type="digit"
						@input="calculateAmount"
					/>
				</uv-form-item>

				<!-- 金额 -->
				<uv-form-item label="金额" prop="amount">
					<uv-input
						v-model="formDetail.amount"
						placeholder="请输入金额"
						type="digit"
					/>
				</uv-form-item>

				<!-- 备注 -->
				<uv-form-item label="备注" prop="remark">
					<uv-input
						v-model="formDetail.remark"
						placeholder="请输入备注"
						type="textarea"
						:maxlength="200"
					/>
				</uv-form-item>

				<!-- 应收数量 -->
				<uv-form-item label="应收数量" prop="receivableQuantity">
					<uv-input
						v-model="formDetail.receivableQuantity"
						placeholder="请输入应收数量"
						type="digit"
						@input="calculateAmount"
					/>
				</uv-form-item>

				<!-- 实收数量 -->
				<uv-form-item label="实收数量" prop="receivedQuantity">
					<uv-input
						v-model="formDetail.receivedQuantity"
						placeholder="请输入实收数量"
						type="digit"
					/>
				</uv-form-item>

				<!-- 基本单位应收数量 -->
				<uv-form-item label="基本单位应收数量" prop="auxReceivableQuantity">
					<uv-input
						v-model="formDetail.auxReceivableQuantity"
						placeholder="请输入基本单位应收数量"
						type="digit"
					/>
				</uv-form-item>

				<!-- 基本单位实收数量 -->
				<uv-form-item label="基本单位实收数量" prop="auxReceivedQuantity">
					<uv-input
						v-model="formDetail.auxReceivedQuantity"
						placeholder="请输入基本单位实收数量"
						type="digit"
					/>
				</uv-form-item>

				<!-- 基本单位 -->
				<uv-form-item label="基本单位" prop="auxUnit">
					<select-picker
						:options="unitOptions"
						:value="getSelectedAuxUnit()"
						:title="'选择基本单位'"
						:labelField="'name'"
						:valueField="'id'"
						:placeholder="'请选择基本单位'"
						:enableLoadMore="true"
						:pageSize="10"
						@input="handleAuxUnitChange"
						@loadMore="loadMoreUnits"
						@search="searchUnits"
						@resetSearch="resetUnitSearch"
					/>
				</uv-form-item>

				<!-- 含税单价 -->
				<uv-form-item label="含税单价" prop="taxPrice">
					<uv-input
						v-model="formDetail.taxPrice"
						placeholder="请输入含税单价"
						type="digit"
					/>
				</uv-form-item>

				<!-- 含税金额 -->
				<uv-form-item label="含税金额" prop="taxAmount">
					<uv-input
						v-model="formDetail.taxAmount"
						placeholder="请输入含税金额"
						type="digit"
					/>
				</uv-form-item>

				<!-- 开票数量 -->
				<uv-form-item label="开票数量" prop="invoiceQuantity">
					<uv-input
						v-model="formDetail.invoiceQuantity"
						placeholder="请输入开票数量"
						type="digit"
					/>
				</uv-form-item>

				<!-- 开票金额 -->
				<uv-form-item label="开票金额" prop="invoiceAmount">
					<uv-input
						v-model="formDetail.invoiceAmount"
						placeholder="请输入开票金额"
						type="digit"
					/>
				</uv-form-item>

				<!-- 开票基本数量 -->
				<uv-form-item label="开票基本数量" prop="invoiceAuxQuantity">
					<uv-input
						v-model="formDetail.invoiceAuxQuantity"
						placeholder="请输入开票基本数量"
						type="digit"
					/>
				</uv-form-item>

				<!-- 生产日期 -->
				<uv-form-item label="生产日期" prop="effictiveDate">
					<uni-datetime-picker
						:value="formDetail.effictiveDate"
						type="date"
						:clear-icon="false"
						placeholder="请选择生产日期"
						@change="dateTimeChange"
					/>
				</uv-form-item>

				<!-- 失效日期 -->
				<uv-form-item label="失效日期" prop="expiryDate">
					<uni-datetime-picker
						:value="formDetail.expiryDate"
						type="date"
						:clear-icon="false"
						placeholder="请选择失效日期"
						@change="expiryDateChange"
					/>
				</uv-form-item>

				<!-- 说明 -->
				<uv-form-item label="说明" prop="note">
					<uv-input
						v-model="formDetail.note"
						placeholder="请输入说明"
						type="textarea"
						:maxlength="200"
					/>
				</uv-form-item>

				<!-- 源单ID -->
				<uv-form-item label="源单ID" prop="sourceId">
					<uv-input
						v-model="formDetail.sourceId"
						placeholder="请输入源单ID"
					/>
				</uv-form-item>

				<!-- 源单单号 -->
				<uv-form-item label="源单单号" prop="sourceNo">
					<uv-input
						v-model="formDetail.sourceNo"
						placeholder="请输入源单单号"
					/>
				</uv-form-item>

				<!-- 批号 -->
				<uv-form-item label="批号" prop="batchNo">
					<uv-input
						v-model="formDetail.batchNo"
						placeholder="请输入批号"
					/>
				</uv-form-item>

				<!-- 成本对象编码 -->
				<uv-form-item label="成本对象编码" prop="costObjectId">
					<uv-input
						v-model="formDetail.costObjectId"
						placeholder="请输入成本对象编码"
					/>
				</uv-form-item>

				<!-- 成本对象名称 -->
				<uv-form-item label="成本对象名称" prop="costObjectName">
					<uv-input
						v-model="formDetail.costObjectName"
						placeholder="请输入成本对象名称"
					/>
				</uv-form-item>

				<!-- 记账凭证号 -->
				<uv-form-item label="记账凭证号" prop="accountingVoucherNumber">
					<uv-input
						v-model="formDetail.accountingVoucherNumber"
						placeholder="请输入记账凭证号"
					/>
				</uv-form-item>
			</uv-form>
		</view>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<Button
				type="primary"
				@click="handleSave"
				:loading="saveLoading"
				:disabled="saveLoading"
				class="save-btn"
			>
				{{ saveLoading ? '保存中...' : (isUpdate ? '保存修改' : '保存') }}
			</Button>
		</view>
	</view>
</template>

<script>
import { getMaterialPageApi, getMaterialApi } from '@/api/scm/base/material/index.js'
import { getUnitApi, getUnitPageApi } from '@/api/scm/base/unit/index.js'
import { getWarehouseLocationPageApi } from '@/api/scm/inventory/warehouseLocation/index.js'
import SelectPicker from '@/components/SelectPicker/SelectPicker.vue'

export default {
	components: {
		SelectPicker
	},
	data() {
		return {
			formDetail: {
				id: undefined,
				num: undefined,
				bizOrderId: undefined,
				bizOrderNo: undefined,
				warehouseId: undefined,
				locationId: undefined,
				materialId: undefined,
				materialName: undefined,
				materialCode: undefined,
				unit: undefined,
				unitName: undefined,
				unitPrice: undefined,
				amount: undefined,
				remark: undefined,
				receivableQuantity: undefined,
				receivedQuantity: undefined,
				auxReceivableQuantity: undefined,
				auxReceivedQuantity: undefined,
				auxUnit: undefined,
				taxPrice: undefined,
				taxAmount: undefined,
				invoiceQuantity: undefined,
				invoiceAmount: undefined,
				invoiceAuxQuantity: undefined,
				effictiveDate: undefined,
				expiryDate: undefined,
				note: undefined,
				sourceId: undefined,
				sourceNo: undefined,
				batchNo: undefined,
				costObjectId: undefined,
				costObjectName: undefined,
				accountingVoucherNumber: undefined
			},
			isUpdate: false,
			saveLoading: false,

			// 选项数据
			locationOptions: [],
			materialOptions: [],
			unitOptions: [],

			// 分页参数
			materialPageParams: {
				pageNo: 1,
				pageSize: 10,
				name: undefined
			},
			materialHasMore: true,

			unitPageParams: {
				pageNo: 1,
				pageSize: 10,
				name: undefined
			},
			unitHasMore: true,

			// 表单验证规则（暂时不启用）
			formRules: {
				// num: [
				// 	{ required: true, message: '请输入序号', trigger: 'blur' }
				// ],
				// materialId: [
				// 	{ required: true, message: '请选择物料', trigger: 'change' }
				// ],
				// unitPrice: [
				// 	{ required: true, message: '请输入单价', trigger: 'blur' }
				// ]
			}
		}
	},
	methods: {
		// 初始化数据
		async initData() {
			await this.loadLocationOptions()
			await this.loadMaterialOptions()
			await this.loadUnitOptions()
		},

		// 初始化编辑数据
		async initEditData(detailForm) {
			try {
				// 如果有物料ID，需要确保物料在选择器中可见
				if (detailForm.materialId) {
					await this.ensureMaterialInOptions(detailForm.materialId)
				}

				// 如果有库位ID，需要确保库位在选择器中可见
				if (detailForm.locationId) {
					await this.ensureLocationInOptions(detailForm.locationId)
				}

				// 如果有基本单位ID，需要确保单位在选择器中可见
				if (detailForm.auxUnit) {
					await this.ensureUnitInOptions(detailForm.auxUnit)
				}

				// 如果有物料但没有单位名称，尝试获取单位信息
				if (detailForm.materialId && !detailForm.unitName && detailForm.unit) {
					try {
						const unitResponse = await getUnitApi(detailForm.unit)
						if (unitResponse.code === 0) {
							this.formDetail.unitName = unitResponse.data.name
						}
					} catch (error) {
						console.error('获取单位信息失败:', error)
					}
				}

				// 处理日期字段，确保uni-datetime-picker能正确显示
				this.initializeDateFields(detailForm)
			} catch (error) {
				console.error('初始化编辑数据失败:', error)
			}
		},

		// 初始化日期字段，确保uni-datetime-picker能正确显示
		initializeDateFields(detailForm) {
			// 处理生产日期
			if (detailForm.effictiveDate) {
				this.formDetail.effictiveDate = this.formatDateForPicker(detailForm.effictiveDate)
			}

			// 处理失效日期
			if (detailForm.expiryDate) {
				this.formDetail.expiryDate = this.formatDateForPicker(detailForm.expiryDate)
			}
		},

		// 格式化日期供uni-datetime-picker使用
		formatDateForPicker(dateValue) {
			if (!dateValue) return undefined

			try {
				let date

				// 如果是字符串
				if (typeof dateValue === 'string') {
					// 处理可能的日期格式
					if (dateValue.includes('T')) {
						// ISO格式: 2024-01-01T00:00:00.000Z
						date = new Date(dateValue)
					} else if (dateValue.includes('-')) {
						// 日期格式: 2024-01-01 或 2024-01-01 00:00:00
						date = new Date(dateValue.replace(/-/g, '/')) // iOS兼容性
					} else {
						date = new Date(dateValue)
					}
				}
				// 如果是时间戳
				else if (typeof dateValue === 'number') {
					date = new Date(dateValue)
				}
				// 如果是Date对象
				else if (dateValue instanceof Date) {
					date = dateValue
				}

				// 检查日期是否有效
				if (date && !isNaN(date.getTime())) {
					// 返回YYYY-MM-DD格式，uni-datetime-picker支持这种格式
					return date.toISOString().split('T')[0]
				}
			} catch (error) {
				console.error('日期格式化失败:', error, dateValue)
			}

			return undefined
		},
		dateTimeChange(time){
			if(time){
			  const date = new Date(time.replace(/-/g, '/'));
			  this.formDetail.effictiveDate = date.getTime()
			}
		},
		expiryDateChange(time){
			if(time){
			  const date = new Date(time.replace(/-/g, '/'));
			  this.formDetail.expiryDate = date.getTime()
			}
		},
		// 确保物料在选择器选项中
		async ensureMaterialInOptions(materialId) {
			// 检查物料是否已在选项中
			const existingMaterial = this.materialOptions.find(item => item.id === materialId)
			if (existingMaterial) {
				return
			}

			try {
				// 获取物料详情并添加到选项中
				const response = await getMaterialApi(materialId)
				if (response.code === 0) {
					const material = response.data
					// 将物料添加到选项列表的开头
					this.materialOptions.unshift(material)
				}
			} catch (error) {
				console.error('获取物料详情失败:', error)
			}
		},

		// 确保库位在选择器选项中
		async ensureLocationInOptions(locationId) {
			// 检查库位是否已在选项中
			const existingLocation = this.locationOptions.find(item => item.id === locationId)
			if (existingLocation) {
				return
			}

			try {
				// 如果库位不在当前选项中，重新加载库位选项
				// 这里可以考虑调用单个库位详情API，但目前先重新加载所有库位
				await this.loadLocationOptions()
			} catch (error) {
				console.error('确保库位在选项中失败:', error)
			}
		},

		// 确保单位在选择器选项中
		async ensureUnitInOptions(unitId) {
			// 检查单位是否已在选项中
			const existingUnit = this.unitOptions.find(item => item.id === unitId)
			if (existingUnit) {
				return
			}

			try {
				// 获取单位详情并添加到选项中
				const response = await getUnitApi(unitId)
				if (response.code === 0) {
					const unit = response.data
					// 将单位添加到选项列表的开头
					this.unitOptions.unshift(unit)
				}
			} catch (error) {
				console.error('获取单位详情失败:', error)
			}
		},

		// 加载库位选项
		async loadLocationOptions() {
			try {
				const response = await getWarehouseLocationPageApi({
					pageNo: 1,
					pageSize: 100
				})
				if (response.code === 0) {
					this.locationOptions = response.data.list || []
				}
			} catch (error) {
				console.error('加载库位选项失败:', error)
				uni.showToast({
					title: '加载库位选项失败',
					icon: 'none'
				})
			}
		},

		// 加载物料选项
		async loadMaterialOptions(reset = false) {
			if (reset) {
				this.materialPageParams.pageNo = 1
				this.materialOptions = []
				this.materialHasMore = true
			}

			if (!this.materialHasMore) return

			try {
				const params = {
					...this.materialPageParams,
					type: '2' // 物料类型
				}

				const response = await getMaterialPageApi(params)
				if (response.code === 0) {
					const newData = response.data.list || []
					if (reset) {
						this.materialOptions = newData
					} else {
						this.materialOptions = [...this.materialOptions, ...newData]
					}

					this.materialHasMore = newData.length === this.materialPageParams.pageSize
					this.materialPageParams.pageNo++
				}
			} catch (error) {
				console.error('加载物料选项失败:', error)
				uni.showToast({
					title: '加载物料选项失败',
					icon: 'none'
				})
			}
		},

		// 加载更多物料
		loadMoreMaterials() {
			this.loadMaterialOptions()
		},

		// 搜索物料
		searchMaterials(keyword) {
			this.materialPageParams.name = keyword
			this.loadMaterialOptions(true)
		},

		// 重置物料搜索
		resetMaterialSearch() {
			this.materialPageParams.name = undefined
			this.loadMaterialOptions(true)
		},

		// 加载单位选项
		async loadUnitOptions(reset = false) {
			if (reset) {
				this.unitPageParams.pageNo = 1
				this.unitOptions = []
				this.unitHasMore = true
			}

			if (!this.unitHasMore) return

			try {
				const response = await getUnitPageApi(this.unitPageParams)
				if (response.code === 0) {
					const newData = response.data.list || []
					if (reset) {
						this.unitOptions = newData
					} else {
						this.unitOptions = [...this.unitOptions, ...newData]
					}

					this.unitHasMore = newData.length === this.unitPageParams.pageSize
					this.unitPageParams.pageNo++
				}
			} catch (error) {
				console.error('加载单位选项失败:', error)
				uni.showToast({
					title: '加载单位选项失败',
					icon: 'none'
				})
			}
		},

		// 加载更多单位
		loadMoreUnits() {
			this.loadUnitOptions()
		},

		// 搜索单位
		searchUnits(keyword) {
			this.unitPageParams.name = keyword
			this.loadUnitOptions(true)
		},

		// 重置单位搜索
		resetUnitSearch() {
			this.unitPageParams.name = undefined
			this.loadUnitOptions(true)
		},

		// 获取选中的库位对象
		getSelectedLocation() {
			if (!this.formDetail.locationId) return null
			return this.locationOptions.find(item => item.id === this.formDetail.locationId) || this.formDetail.locationId
		},

		// 获取选中的物料对象
		getSelectedMaterial() {
			if (!this.formDetail.materialId) return null
			return this.materialOptions.find(item => item.id === this.formDetail.materialId) || this.formDetail.materialId
		},

		// 获取选中的基本单位对象
		getSelectedAuxUnit() {
			if (!this.formDetail.auxUnit) return null
			return this.unitOptions.find(item => item.id === this.formDetail.auxUnit) || this.formDetail.auxUnit
		},

		// 库位选择变化
		handleLocationChange(value) {
			this.formDetail.locationId = value?.id || value
		},

		// 基本单位选择变化
		handleAuxUnitChange(value) {
			if (!value) {
				this.formDetail.auxUnit = undefined
				return
			}

			this.formDetail.auxUnit = value.id || value
		},

		// 物料选择变化
		async handleMaterialChange(value) {
			if (!value) {
				this.formDetail.materialId = undefined
				this.formDetail.materialName = undefined
				this.formDetail.materialCode = undefined
				this.formDetail.unit = undefined
				this.formDetail.unitName = undefined
				return
			}

			try {
				const materialId = value.id || value

				// 如果选择的是同一个物料，不需要重新获取信息
				if (this.formDetail.materialId === materialId && this.formDetail.materialName) {
					return
				}

				this.formDetail.materialId = materialId

				// 先尝试从选项中获取物料信息
				let material = null
				if (typeof value === 'object' && value.fullName) {
					material = value
				} else {
					// 从选项列表中查找
					material = this.materialOptions.find(item => item.id === materialId)
				}

				// 如果选项中没有完整信息，则调用API获取
				if (!material || !material.fullName) {
					const response = await getMaterialApi(materialId)
					if (response.code === 0) {
						material = response.data
					}
				}

				if (material) {
					this.formDetail.materialName = material.fullName
					this.formDetail.materialCode = material.fullCode

					// 获取单位信息
					if (material.unit) {
						this.formDetail.unit = material.unit

						// 如果没有单位名称，则获取单位详情
						if (!this.formDetail.unitName) {
							const unitResponse = await getUnitApi(material.unit)
							if (unitResponse.code === 0) {
								this.formDetail.unitName = unitResponse.data.name
							}
						}
					}
				}
			} catch (error) {
				console.error('获取物料信息失败:', error)
				uni.showToast({
					title: '获取物料信息失败',
					icon: 'none'
				})
			}
		},

		// 计算金额
		calculateAmount() {
			const unitPrice = parseFloat(this.formDetail.unitPrice) || 0
			const quantity = parseFloat(this.formDetail.receivableQuantity) || 1
			this.formDetail.amount = (unitPrice * quantity).toFixed(2)
		},

		// 保存数据
		async handleSave() {
			try {
				this.saveLoading = true

				// 基本数据检查（非严格验证）
				const checkResult = this.basicDataCheck()
				if (!checkResult.valid) {
					uni.showToast({
						title: checkResult.message,
						icon: 'none',
						duration: 2000
					})
					return
				}

				// 数据预处理
				const formData = this.preprocessFormData()

				// 通知父页面保存数据
				const eventChannel = this.getOpenerEventChannel()
				if (eventChannel) {
					eventChannel.emit('saveFormDetail', {
						formDetail: formData,
						isUpdate: this.isUpdate
					})

					// 显示保存成功提示
					uni.showToast({
						title: this.isUpdate ? '修改成功' : '添加成功',
						icon: 'success',
						duration: 1500
					})

					// 延迟返回，让用户看到成功提示
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					// 如果没有事件通道，直接返回
					uni.showToast({
						title: '保存失败，请重试',
						icon: 'none'
					})
				}

			} catch (error) {
				console.error('保存失败:', error)
				uni.showToast({
					title: '保存失败，请重试',
					icon: 'none'
				})
			} finally {
				this.saveLoading = false
			}
		},

		// 基本数据检查
		basicDataCheck() {
			// 检查是否有基本的必要信息
			if (!this.formDetail.materialId && !this.formDetail.materialName) {
				return {
					valid: false,
					message: '请选择物料'
				}
			}

			// 检查数量字段是否为负数
			const quantityFields = [
				{ field: 'receivableQuantity', name: '应收数量' },
				{ field: 'receivedQuantity', name: '实收数量' },
				{ field: 'auxReceivableQuantity', name: '基本单位应收数量' },
				{ field: 'auxReceivedQuantity', name: '基本单位实收数量' },
				{ field: 'invoiceQuantity', name: '开票数量' },
				{ field: 'invoiceAuxQuantity', name: '开票基本数量' }
			]

			for (const item of quantityFields) {
				const value = parseFloat(this.formDetail[item.field])
				if (!isNaN(value) && value < 0) {
					return {
						valid: false,
						message: `${item.name}不能为负数`
					}
				}
			}

			// 检查金额字段是否为负数
			const amountFields = [
				{ field: 'unitPrice', name: '单价' },
				{ field: 'amount', name: '金额' },
				{ field: 'taxPrice', name: '含税单价' },
				{ field: 'taxAmount', name: '含税金额' },
				{ field: 'invoiceAmount', name: '开票金额' }
			]

			for (const item of amountFields) {
				const value = parseFloat(this.formDetail[item.field])
				if (!isNaN(value) && value < 0) {
					return {
						valid: false,
						message: `${item.name}不能为负数`
					}
				}
			}

			// 检查日期逻辑
			if (this.formDetail.effictiveDate && this.formDetail.expiryDate) {
				const effectiveDate = new Date(this.formDetail.effictiveDate)
				const expiryDate = new Date(this.formDetail.expiryDate)
			}

			return { valid: true }
		},

		// 数据预处理
		preprocessFormData() {
			const formData = { ...this.formDetail }

			// 处理数字类型字段
			const numberFields = [
				'num', 'unitPrice', 'amount', 'receivableQuantity', 'receivedQuantity',
				'auxReceivableQuantity', 'auxReceivedQuantity', 'taxPrice', 'taxAmount',
				'invoiceQuantity', 'invoiceAmount', 'invoiceAuxQuantity'
			]

			numberFields.forEach(field => {
				if (formData[field] !== undefined && formData[field] !== null && formData[field] !== '') {
					formData[field] = parseFloat(formData[field]) || 0
				}
			})
			// 清理空值字段
			Object.keys(formData).forEach(key => {
				if (formData[key] === '' || formData[key] === null) {
					formData[key] = undefined
				}
			})

			return formData
		},
	},

	onLoad() {
		const eventChannel = this.getOpenerEventChannel()
		if (!eventChannel) {
			return
		}

		eventChannel.on('acceptDataFormOpener', async (data) => {
			if (data) {
				// 先合并数据
				this.formDetail = { ...this.formDetail, ...data.detailForm }
				this.isUpdate = data.isUpdate

				// 确保基础数据已加载
				await this.initData()

				// 如果是编辑模式且有数据，需要初始化相关信息
				if (this.isUpdate && data.detailForm) {
					await this.initEditData(data.detailForm)
				}

				// 强制更新视图
				this.$forceUpdate()
			}
		})
	},

	async onReady() {
		// 如果还没有接收到数据，先初始化基础数据
		if (!this.isUpdate) {
			await this.initData()
		}
	}
}
</script>

<style scoped>
.edit-form-detail-page {
	min-height: 100vh;
	background-color: #fff;
}

.form-container {
	padding: 20rpx 20rpx 160rpx 20rpx; /* 底部增加内边距，避免被按钮和日期选择器挡住 */
	background-color: #ffffff;
	margin: 20rpx;
	border-radius: 12rpx;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 20rpx;
	background-color: #ffffff;
	border-top: 1rpx solid #e5e5e5;
	z-index: 10; /* 降低z-index，避免遮挡日期选择器 */
}

.save-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	background-color: #007AFF;
	color: #ffffff;
	border: none;
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
	transition: all 0.3s ease;
}

.save-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.save-btn[disabled] {
	background-color: #c0c4cc;
	box-shadow: none;
	transform: none;
}
</style>