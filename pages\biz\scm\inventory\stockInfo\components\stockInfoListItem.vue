<template>
	<view class="list-item-wrapper">
		<view class="stock-card" @click="handleItemClick">
			<!-- 物料基本信息 -->
			<view class="material-info">
				<view class="material-header">
					<view class="material-title">
						<text class="material-name">{{ item.materialName || '未知物料' }}</text>
						<text class="material-code">{{ item.materialCode || '-' }}</text>
					</view>
					<view class="material-badges">
						<view class="material-type" :class="item.materialTypeClass">
							{{ getMaterialTypeText(item.materialType) }}
						</view>
						<view class="status-tag" :class="item.statusClass">
							{{ getStatusText(item.status) }}
						</view>
					</view>
				</view>
			</view>

			<!-- 核心库存信息 -->
			<view class="core-info">
				<view class="core-item primary">
					<text class="core-label">库存数量</text>
					<text class="core-value">{{ formatQuantity(item.quantity) }} {{ getUnitName(item.quantityUnit) }}</text>
				</view>
				<view class="core-item">
					<text class="core-label">可用数量</text>
					<text class="core-value">{{ formatQuantity(item.unlockQuantity) }} {{ getUnitName(item.quantityUnit) }}</text>
				</view>
				<view class="core-item">
					<text class="core-label">仓库</text>
					<text class="core-value">{{ item.warehouseName || '-' }}</text>
				</view>
			</view>

			<!-- 展开/收起按钮 -->
			<view class="expand-section" @click="toggleExpand">
				<text class="expand-text">{{ isExpanded ? '收起详情' : '查看更多' }}</text>
				<uni-icons
					:type="isExpanded ? 'up' : 'down'"
					size="16"
					color="#666"
				></uni-icons>
			</view>

			<!-- 详细数量信息（展开时显示） -->
			<view class="detailed-quantities" v-show="isExpanded">
				<view class="section-title">详细数量信息</view>
				<view class="quantity-grid">
					<view class="quantity-item">
						<text class="quantity-label">锁定数量</text>
						<text class="quantity-value">{{ formatQuantity(item.lockQuantity) }} {{ getUnitName(item.quantityUnit) }}</text>
					</view>
					<view class="quantity-item">
						<text class="quantity-label">在途数量</text>
						<text class="quantity-value">{{ formatQuantity(item.transitQuantity) }} {{ getUnitName(item.quantityUnit) }}</text>
					</view>
					<view class="quantity-item">
						<text class="quantity-label">在途已锁</text>
						<text class="quantity-value">{{ formatQuantity(item.lockTransitQuantity || 0) }} {{ getUnitName(item.quantityUnit) }}</text>
					</view>
					<view class="quantity-item">
						<text class="quantity-label">在途未锁</text>
						<text class="quantity-value">{{ formatQuantity(item.unlockTransitQuantity || 0) }} {{ getUnitName(item.quantityUnit) }}</text>
					</view>
				</view>

				<!-- 其他信息 -->
				<view class="other-info">
					<view class="info-item">
						<text class="info-label">物料来源</text>
						<text class="info-value">{{ getMaterialSourceText(item.materialSource) }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">单价</text>
						<text class="info-value">{{ formatAmount(item.price) }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">总价值</text>
						<text class="info-value">{{ formatAmount(item.totalCost) }}</text>
					</view>
				</view>
			</view>

			<!-- 展开内容区域 -->
			<view class="detail-content" v-show="isExpanded">
				<view class="detail-tabs">
					<view
						class="tab-item"
						:class="{ active: activeTab === 'transaction' }"
						@click="switchTab('transaction')"
					>
						历史交易
					</view>
					<view
						class="tab-item"
						:class="{ active: activeTab === 'batch' }"
						@click="switchTab('batch')"
					>
						批次信息
					</view>
				</view>

				<!-- 历史交易内容 -->
				<view v-if="activeTab === 'transaction'" class="transaction-content">
					<view v-if="transactionLoading" class="loading-container">
						<uni-load-more status="loading"></uni-load-more>
					</view>
					<view v-else-if="transactionList.length === 0" class="empty-container">
						<text class="empty-text">暂无历史交易记录</text>
					</view>
					<view v-else class="transaction-list">
						<view
							v-for="transaction in transactionList"
							:key="transaction.id"
							class="transaction-item"
						>
							<view class="transaction-header">
								<text class="transaction-type">{{ getTransactionTypeText(transaction.transactionType) }} - {{ getTransactionDirectionText(transaction.transactionDirection) }}</text>
								<text class="transaction-date">{{ formatDate(transaction.moveDate || transaction.createTime) }}</text>
							</view>
							<view class="transaction-details">
								<text class="transaction-no">单号：{{ transaction.bizNo || transaction.documentNo || '-' }}</text>
								<text class="transaction-batch">批号：{{ transaction.inventoryBatchNo || '-' }}</text>
							</view>
							<view class="transaction-quantities">
								<text class="transaction-quantity">
									数量：{{ formatQuantity(transaction.quantity) }} {{ getUnitName(transaction.quantityUnit) }}
								</text>
								<text v-if="transaction.lockQuantity" class="lock-quantity">
									锁定：{{ formatQuantity(transaction.lockQuantity) }} {{ getUnitName(transaction.quantityUnit) }}
								</text>
							</view>
							<view class="transaction-before-after">
								<text class="before-quantity">
									出入库前：{{ formatQuantity(transaction.beforeQuantity) }} {{ getUnitName(transaction.quantityUnit) }}
								</text>
								<text class="after-quantity">
									出入库后：{{ formatQuantity(transaction.afterQuantity) }} {{ getUnitName(transaction.quantityUnit) }}
								</text>
							</view>
							<view class="transaction-warehouse">
								<text class="from-warehouse">
									来源仓库：{{ transaction.fromWarehouseName || '-' }}
								</text>
								<text class="to-warehouse">
									目标仓库：{{ transaction.toWarehouseName || '-' }}
								</text>
							</view>

							<view v-if="transaction.remark" class="transaction-remark">
								<text class="remark-text">备注：{{ transaction.remark }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 批次信息内容 -->
				<view v-if="activeTab === 'batch'" class="batch-content">
					<view v-if="batchLoading" class="loading-container">
						<uni-load-more status="loading"></uni-load-more>
					</view>
					<view v-else-if="batchList.length === 0" class="empty-container">
						<text class="empty-text">暂无批次信息</text>
					</view>
					<view v-else class="batch-list">
						<view
							v-for="batch in batchList"
							:key="batch.id"
							class="batch-item"
						>
							<!-- 批次标题 -->
							<view class="batch-header">
								<text class="batch-no">{{ batch.batchNo || '-' }}</text>
								<view class="status-tag" :class="'batch-status-' + (batch.status || 'default')">
									{{ getBatchStatusText(batch.status) }}
								</view>
							</view>

							<!-- 数量信息 -->
							<view class="batch-section">
								<view class="section-title">数量信息</view>
								<view class="info-row">
									<view class="info-item">
										<text class="info-label">总数量</text>
										<text class="info-value">{{ formatQuantity(batch.quantity) }} {{ getUnitName(batch.quantityUnit) }}</text>
									</view>
									<view class="info-item">
										<text class="info-label">库存数量</text>
										<text class="info-value">{{ formatQuantity(batch.inventoryQuantity) }} {{ getUnitName(batch.quantityUnit) }}</text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item">
										<text class="info-label">锁定数量</text>
										<text class="info-value">{{ formatQuantity(batch.lockQuantity) }} {{ getUnitName(batch.quantityUnit) }}</text>
									</view>
									<view class="info-item">
										<text class="info-label">未锁数量</text>
										<text class="info-value">{{ formatQuantity(batch.unlockQuantity) }} {{ getUnitName(batch.quantityUnit) }}</text>
									</view>
								</view>
							</view>

							<!-- 基本信息 -->
							<view class="batch-section">
								<view class="section-title">基本信息</view>
								<view class="info-row">
									<view class="info-item">
										<text class="info-label">生产日期</text>
										<text class="info-value">{{ formatDate(batch.productionDate || batch.inDate) }}</text>
									</view>
									<view class="info-item">
										<text class="info-label">有效期</text>
										<text class="info-value">{{ formatDate(batch.expiryDate) || '-' }}</text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item">
										<text class="info-label">供应商</text>
										<text class="info-value">{{ batch.supplierName || batch.objectName || '-' }}</text>
									</view>
									<view class="info-item">
										<text class="info-label">仓库</text>
										<text class="info-value">{{ batch.warehouseName || '-' }}</text>
									</view>
								</view>
								<view class="info-row" v-if="batch.locations">
									<view class="info-item full-width">
										<text class="info-label">库位</text>
										<text class="info-value">{{ batch.locations || '-' }}</text>
									</view>
								</view>
							</view>

							<!-- 价格信息 -->
							<view class="batch-section" v-if="batch.price">
								<view class="section-title">价格信息</view>
								<view class="info-row">
									<view class="info-item">
										<text class="info-label">单价</text>
										<text class="info-value">{{ formatPrice(batch.price) }} {{ batch.priceUnit || '元' }}</text>
									</view>
									<view class="info-item" v-if="batch.totalCost">
										<text class="info-label">总价值</text>
										<text class="info-value">{{ formatPrice(batch.totalCost) }} 元</text>
									</view>
								</view>
							</view>

							<!-- 备注信息 -->
							<view class="batch-section" v-if="batch.remark">
								<view class="section-title">备注</view>
								<text class="remark-text">{{ batch.remark }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getDictLabel, DICT_TYPE } from '../../../../../../utils/dict.js'
import { getTransactionListByInventoryIdApi } from '../../../../../../api/scm/inventory/stockInfo/index.js'
import { getBatchInfoPageApi } from '../../../../../../api/scm/inventory/batchInfo/index.js'

export default {
	name: 'StockInfoListItem',
	props: {
		// 库存项数据
		item: {
			type: Object,
			required: true
		},
		// 物料类型选项
		materialTypeOptions: {
			type: Array,
			default: () => []
		},
		// 状态选项
		statusOptions: {
			type: Array,
			default: () => []
		},
		// 单位选项
		unitOptions: {
			type: Array,
			default: () => []
		},
		// 字典数据
		dictData: {
			type: Object,
			default: () => ({})
		},
		// 是否展开状态（由父组件控制）
		isExpanded: {
			type: Boolean,
			default: false
		}
	},
	mounted() {
	},
	watch: {
		// 监听展开状态变化
		isExpanded(newVal) {
			if (newVal) {
				// 展开时加载对应Tab的数据
				if (this.activeTab === 'transaction' && this.transactionList.length === 0) {
					this.loadTransactionList()
				} else if (this.activeTab === 'batch' && this.batchList.length === 0) {
					this.loadBatchList()
				}
			}
		}
	},
	data() {
		return {
			// 当前激活的Tab
			activeTab: 'transaction',
			// 历史交易数据
			transactionList: [],
			transactionLoading: false,
			// 批次信息数据
			batchList: [],
			batchLoading: false
		}
	},
	methods: {
		// 处理列表项点击事件
		handleItemClick() {
			this.$emit('item-click', this.item);
		},

		// 格式化数量
		formatQuantity(value) {
			if (value === null || value === undefined || value === '') return '0'
			if (isNaN(value)) return '0'
			return Number(value).toLocaleString()
		},

		// 格式化价格
		formatPrice(price) {
			if (price === null || price === undefined || price === '') return '0.00'
			if (isNaN(price)) return '0.00'
			return Number(price).toFixed(2)
		},

		// 格式化金额
		formatAmount(value) {
			if (value === null || value === undefined || value === '') return '¥0.00'
			if (isNaN(value)) return '¥0.00'
			return '¥' + Number(value).toFixed(2)
		},

		// 获取单位名称
		getUnitName(unitId) {
			if (!unitId && unitId !== 0) return ''

			// 确保unitOptions存在且为数组
			if (!Array.isArray(this.unitOptions)) {
				return unitId.toString()
			}

			// 处理数字类型
			if (typeof unitId === 'number') {
				const unit = this.unitOptions.find(unit => unit.id === unitId)
				if (unit && unit.name) return unit.name
			}

			// 处理字符串类型
			if (typeof unitId === 'string') {
				// 先尝试按字符串查找
				let unit = this.unitOptions.find(unit => unit.id && unit.id.toString() === unitId)
				if (unit && unit.name) return unit.name

				// 再尝试转换为数字查找
				const numId = parseInt(unitId)
				if (!isNaN(numId)) {
					unit = this.unitOptions.find(unit => unit.id === numId)
					if (unit && unit.name) return unit.name
				}
			}

			return unitId ? unitId.toString() : ''
		},

		// 获取物料类型文本
		getMaterialTypeText(type) {
			if (!type) return '未知'
			if (!Array.isArray(this.materialTypeOptions)) {
				return type
			}
			return getDictLabel(this.materialTypeOptions, type) || type || '未知'
		},

		// 获取物料来源文本
		getMaterialSourceText(source) {
			if (!source) return '未知'
			if (!this.dictData || typeof this.dictData !== 'object') {
				return source
			}
			const materialSourceOptions = this.dictData[DICT_TYPE.MATERIAL_SOURCE] || []
			return getDictLabel(materialSourceOptions, source) || source || '未知'
		},

		// 获取状态文本
		getStatusText(status) {
			if (!status) return '未知'
			if (!Array.isArray(this.statusOptions)) {
				return status
			}
			return getDictLabel(this.statusOptions, status) || status || '未知'
		},

		// 切换展开状态
		toggleExpand() {
			// 通知父组件切换展开状态
			this.$emit('toggle-expand', this.item.id)
		},

		// 切换Tab
		switchTab(tab) {
			this.activeTab = tab
			// 只有在展开状态下才加载数据
			if (this.isExpanded) {
				if (tab === 'transaction' && this.transactionList.length === 0) {
					this.loadTransactionList()
				} else if (tab === 'batch' && this.batchList.length === 0) {
					this.loadBatchList()
				}
			}
		},

		// 加载历史交易数据
		async loadTransactionList() {
			if (!this.item.id) {
				return
			}

			this.transactionLoading = true
			try {
				// 调用API获取历史交易数据
				const response = await getTransactionListByInventoryIdApi(this.item.id)

				// 根据实际API响应结构调整数据获取方式
				this.transactionList = response.data || response || []
			} catch (error) {
				this.$modal.msgError('获取历史交易记录失败')
				this.transactionList = []
			} finally {
				this.transactionLoading = false
			}
		},

		// 加载批次信息数据
		async loadBatchList() {
			if (!this.item.id) {
				return
			}

			this.batchLoading = true
			try {
				// 调用API获取批次信息数据
				const response = await getBatchInfoPageApi({
					pageNo: 1,
					pageSize: 100, // 获取所有批次信息
					inventoryId: this.item.id
				})

				// 根据实际API响应结构调整数据获取方式
				this.batchList = response.data?.list || response.data || response || []
			} catch (error) {
				this.$modal.msgError('获取批次信息失败')
				this.batchList = []
			} finally {
				this.batchLoading = false
			}
		},

		// 格式化日期
		formatDate(dateStr) {
			if (!dateStr) return '-'
			try {
				const date = new Date(dateStr)
				if (isNaN(date.getTime())) return '-'
				return `${date.getMonth() + 1}/${date.getDate()}`
			} catch (error) {
				return '-'
			}
		},

		// 获取交易类型文本
		getTransactionTypeText(type) {
			if (!type) return '未知'
			if (!this.dictData || typeof this.dictData !== 'object') {
				return type
			}
			// 使用字典数据获取交易类型
			const transactionTypeOptions = this.dictData[DICT_TYPE.SCM_BIZ_TYPE] || []
			return getDictLabel(transactionTypeOptions, type) || type || '未知'
		},

		// 获取交易方向文本
		getTransactionDirectionText(direction) {
			if (!direction) return '未知'
			if (!this.dictData || typeof this.dictData !== 'object') {
				return direction
			}
			// 使用库存交易方向字典数据
			const inventoryTransactionDirectionOptions = this.dictData[DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION] || []
			return getDictLabel(inventoryTransactionDirectionOptions, direction) || direction || '未知'
		},



		// 获取批次状态文本
		getBatchStatusText(status) {
			if (!status) return '未知'
			if (!this.dictData || typeof this.dictData !== 'object') {
				return status
			}
			// 使用字典数据获取批次状态 - 使用库存状态字典
			const batchStatusOptions = this.dictData[DICT_TYPE.STOCK_STATUS] || []
			return getDictLabel(batchStatusOptions, status) || status || '未知'
		}
	}
}
</script>

<style lang="scss" scoped>
.list-item-wrapper {
	margin-bottom: 24rpx;
}

.stock-card {
	background-color: white;
	border-radius: 20rpx;
	padding: 28rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
	border: 1px solid #f0f0f0;
	transition: box-shadow 0.2s, transform 0.2s;

	&:active {
		transform: translateY(1rpx);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
	}
}

.material-info {
	margin-bottom: 24rpx;

	.material-header {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		gap: 16rpx;

		.material-title {
			flex: 1;
			min-width: 0;

			.material-name {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				line-height: 1.4;
				margin-bottom: 6rpx;
				display: block;
			}

			.material-code {
				font-size: 24rpx;
				color: #666;
				line-height: 1.3;
			}
		}

		.material-badges {
			display: flex;
			flex-direction: column;
			gap: 8rpx;
			align-items: flex-end;
			flex-shrink: 0;

			.material-type {
				padding: 6rpx 12rpx;
				border-radius: 16rpx;
				font-size: 22rpx;
				color: white;
				font-weight: 500;

				&.type-raw { background-color: #409EFF; }
				&.type-semi { background-color: #E6A23C; }
				&.type-finished { background-color: #67C23A; }
				&.type-auxiliary { background-color: #909399; }
				&.type-default { background-color: #C0C4CC; }
			}
		}
	}
}

// 核心信息区域
.core-info {
	display: flex;
	justify-content: space-between;
	gap: 20rpx;
	margin-bottom: 20rpx;

	.core-item {
		flex: 1;
		text-align: center;
		padding: 16rpx 12rpx;
		background-color: #f8f9fa;
		border-radius: 12rpx;

		&.primary {
			background-color: #e8f4ff;

			.core-value {
				color: #409EFF;
				font-weight: bold;
			}
		}

		.core-label {
			font-size: 22rpx;
			color: #666;
			display: block;
			margin-bottom: 8rpx;
			line-height: 1.2;
		}

		.core-value {
			font-size: 28rpx;
			font-weight: 600;
			color: #333;
			line-height: 1.3;
		}
	}
}

.status-tag {
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	font-size: 22rpx;
	color: white;
	line-height: 1.2;
	font-weight: 500;

	&.status-normal { background-color: #67C23A; }
	&.status-locked { background-color: #E6A23C; }
	&.status-frozen { background-color: #409EFF; }
	&.status-damaged { background-color: #F56C6C; }
	&.status-default { background-color: #C0C4CC; }
}

// 展开按钮样式
.expand-section {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 20rpx 0;
	border-top: 1px solid #f0f0f0;
	margin-top: 8rpx;
	cursor: pointer;
	transition: background-color 0.2s;

	&:active {
		background-color: #f8f9fa;
	}

	.expand-text {
		font-size: 26rpx;
		color: #409EFF;
		margin-right: 8rpx;
		font-weight: 500;
	}
}

// 详细数量信息样式
.detailed-quantities {
	border-top: 1px solid #f0f0f0;
	padding-top: 24rpx;
	margin-top: 16rpx;

	.section-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 16rpx;
	}

	.quantity-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 16rpx;
		margin-bottom: 24rpx;

		.quantity-item {
			background-color: #f8f9fa;
			padding: 16rpx;
			border-radius: 12rpx;
			text-align: center;

			.quantity-label {
				font-size: 22rpx;
				color: #666;
				display: block;
				margin-bottom: 8rpx;
				line-height: 1.2;
			}

			.quantity-value {
				font-size: 26rpx;
				font-weight: 600;
				color: #333;
				line-height: 1.3;
			}
		}
	}

	.other-info {
		.info-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 12rpx 0;
			border-bottom: 1px solid #f5f5f5;

			&:last-child {
				border-bottom: none;
			}

			.info-label {
				font-size: 24rpx;
				color: #666;
			}

			.info-value {
				font-size: 24rpx;
				color: #333;
				font-weight: 500;
			}
		}
	}
}

// 详情内容样式
.detail-content {
	border-top: 1px solid #f0f0f0;
	margin-top: 16rpx;
	padding-top: 16rpx;
}

// Tab切换样式
.detail-tabs {
	display: flex;
	border-bottom: 1px solid #f0f0f0;
	margin-bottom: 16rpx;

	.tab-item {
		flex: 1;
		text-align: center;
		padding: 16rpx 0;
		font-size: 26rpx;
		color: #666;
		border-bottom: 2rpx solid transparent;
		cursor: pointer;

		&.active {
			color: #2979ff;
			border-bottom-color: #2979ff;
			font-weight: bold;
		}
	}
}

// 加载和空状态样式
.loading-container,
.empty-container {
	padding: 40rpx 0;
	text-align: center;

	.empty-text {
		font-size: 24rpx;
		color: #999;
	}
}

// 历史交易样式
.transaction-list {
	.transaction-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 16rpx;
		border: 1px solid #e5e5e5;

		&:last-child {
			margin-bottom: 0;
		}

		.transaction-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;
			padding-bottom: 12rpx;
			border-bottom: 1px solid #f0f0f0;

			.transaction-type {
				font-size: 28rpx;
				font-weight: 600;
				color: #333;
			}

			.transaction-date {
				font-size: 24rpx;
				color: #666;
			}
		}

		.transaction-details {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 12rpx 0;

			.transaction-no {
				font-size: 24rpx;
				color: #666;
			}

			.transaction-batch {
				font-size: 24rpx;
				color: #666;
			}
		}

		.transaction-quantities {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 12rpx 0;

			.transaction-quantity {
				font-size: 24rpx;
				color: #666;
			}

			.lock-quantity {
				font-size: 24rpx;
				color: #666;
			}
		}

		.transaction-before-after {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 12rpx 0;

			.before-quantity {
				font-size: 24rpx;
				color: #666;
			}

			.after-quantity {
				font-size: 24rpx;
				color: #666;
			}
		}

		.transaction-warehouse {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 12rpx 0;

			.from-warehouse {
				font-size: 24rpx;
				color: #666;
			}

			.to-warehouse {
				font-size: 24rpx;
				color: #666;
			}
		}



		.transaction-remark {
			margin-top: 12rpx;
			padding: 12rpx;
			background-color: #f8f9fa;
			border-radius: 8rpx;

			.remark-text {
				font-size: 22rpx;
				color: #666;
				line-height: 1.5;
			}
		}
	}
}

// 批次信息样式
.batch-list {
	.batch-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 16rpx;
		border: 1px solid #e5e5e5;

		&:last-child {
			margin-bottom: 0;
		}

		.batch-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;
			padding-bottom: 16rpx;
			border-bottom: 1px solid #f0f0f0;

			.batch-no {
				font-size: 28rpx;
				font-weight: 600;
				color: #333;
			}

			.status-tag {
				padding: 6rpx 12rpx;
				border-radius: 16rpx;
				font-size: 22rpx;
				color: white;

				&.batch-status-NORMAL,
				&.batch-status-AVAILABLE {
					background-color: #67C23A;
				}

				&.batch-status-EXPIRED,
				&.batch-status-DAMAGED {
					background-color: #F56C6C;
				}

				&.batch-status-LOCKED {
					background-color: #E6A23C;
				}

				&.batch-status-default {
					background-color: #C0C4CC;
				}
			}
		}

		.batch-section {
			margin-bottom: 20rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.section-title {
				font-size: 24rpx;
				font-weight: 600;
				color: #333;
				margin-bottom: 12rpx;
			}

			.info-row {
				display: flex;
				margin-bottom: 12rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.info-item {
					flex: 1;
					display: flex;
					flex-direction: column;

					&.full-width {
						flex: 2;
					}

					&:not(:last-child) {
						margin-right: 20rpx;
					}

					.info-label {
						font-size: 22rpx;
						color: #666;
						margin-bottom: 4rpx;
					}

					.info-value {
						font-size: 24rpx;
						color: #333;
						font-weight: 500;
					}
				}
			}

			.remark-text {
				font-size: 22rpx;
				color: #666;
				line-height: 1.5;
				background-color: #f8f9fa;
				padding: 12rpx;
				border-radius: 8rpx;
			}
		}
	}
}
</style>