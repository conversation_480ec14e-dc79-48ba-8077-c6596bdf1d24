<template>
	<view class="edit-container">
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left" @click="goBack">
				<uni-icons type="left" size="24"></uni-icons>
				<text class="nav-title">编辑库存信息</text>
			</view>
			<view class="nav-right" @click="handleSave">
				<text class="save-btn">保存</text>
			</view>
		</view>

		<!-- 表单内容 -->
		<scroll-view scroll-y class="form-content" v-if="formData">
			<!-- 物料信息 -->
			<view class="form-section">
				<view class="section-title">物料信息</view>
				<view class="form-item">
					<text class="label">物料名称</text>
					<text class="value">{{ formData.materialName || '-' }}</text>
				</view>
				<view class="form-item">
					<text class="label">物料编码</text>
					<text class="value">{{ formData.materialCode || '-' }}</text>
				</view>
				<view class="form-item">
					<text class="label">物料规格</text>
					<text class="value">{{ formData.spec || '-' }}</text>
				</view>
				<view class="form-item">
					<text class="label">物料类型</text>
					<text class="value">{{ getMaterialTypeText(formData.materialType) }}</text>
				</view>
				<view class="form-item">
					<text class="label">物料来源</text>
					<text class="value">{{ getMaterialSourceText(formData.materialSource) }}</text>
				</view>
			</view>

			<!-- 库存信息 -->
			<view class="form-section">
				<view class="section-title">库存信息</view>
				<view class="form-item">
					<text class="label">库存数量</text>
					<text class="value">{{ formatQuantity(formData.quantity) }} {{ getUnitName(formData.quantityUnit) }}</text>
				</view>
				<view class="form-item">
					<text class="label">锁定数量</text>
					<text class="value">{{ formatQuantity(formData.lockQuantity) }} {{ getUnitName(formData.quantityUnit) }}</text>
				</view>
				<view class="form-item">
					<text class="label">可用数量</text>
					<text class="value">{{ formatQuantity(formData.unlockQuantity) }} {{ getUnitName(formData.quantityUnit) }}</text>
				</view>
				<view class="form-item">
					<text class="label">数量单位</text>
					<text class="value">{{ getUnitName(formData.quantityUnit) }}</text>
				</view>
			</view>

			<!-- 可编辑字段 -->
			<view class="form-section">
				<view class="section-title">可编辑信息</view>
				<view class="form-item">
					<text class="label">基本单位数量</text>
					<input 
						class="input" 
						type="number" 
						v-model="formData.auxiliaryQuantity" 
						placeholder="请输入基本单位数量"
					/>
				</view>
				<view class="form-item">
					<text class="label">库存数量</text>
					<input 
						class="input" 
						type="number" 
						v-model="formData.inventoryQuantity" 
						placeholder="请输入库存数量"
					/>
				</view>
				<view class="form-item">
					<text class="label">价格</text>
					<input 
						class="input" 
						type="number" 
						v-model="formData.price" 
						placeholder="请输入价格"
					/>
				</view>
				<view class="form-item">
					<text class="label">总价值</text>
					<input 
						class="input" 
						type="number" 
						v-model="formData.totalCost" 
						placeholder="请输入总价值"
					/>
				</view>
				<view class="form-item">
					<text class="label">采购价格</text>
					<input 
						class="input" 
						type="number" 
						v-model="formData.purchasePrice" 
						placeholder="请输入采购价格"
					/>
				</view>
				<view class="form-item">
					<text class="label">销售价格</text>
					<input 
						class="input" 
						type="number" 
						v-model="formData.salePrice" 
						placeholder="请输入销售价格"
					/>
				</view>
				<view class="form-item">
					<text class="label">备注</text>
					<textarea 
						class="textarea" 
						v-model="formData.remark" 
						placeholder="请输入备注"
						maxlength="200"
					></textarea>
				</view>
			</view>

			<!-- 仓库信息 -->
			<view class="form-section">
				<view class="section-title">仓库信息</view>
				<view class="form-item">
					<text class="label">仓库名称</text>
					<text class="value">{{ formData.warehouseName || '-' }}</text>
				</view>
				<view class="form-item">
					<text class="label">仓位名称</text>
					<text class="value">{{ formData.locationName || '-' }}</text>
				</view>
			</view>

			<!-- 库存交易明细 -->
			<view class="form-section">
				<view class="section-title">库存交易明细</view>
				<view v-if="transactionList.length > 0" class="transaction-list">
					<view v-for="(transaction, index) in transactionList" :key="index" class="transaction-item">
						<view class="transaction-header">
							<text class="transaction-type">{{ getTransactionTypeText(transaction.transactionType) }} - {{ getTransactionDirectionText(transaction.transactionDirection) }}</text>
							<text class="transaction-date">{{ formatDate(transaction.moveDate || transaction.createTime) }}</text>
						</view>
						<view class="transaction-details">
							<text class="transaction-no">单号：{{ transaction.bizNo || transaction.documentNo || '-' }}</text>
							<text class="transaction-batch">批号：{{ transaction.inventoryBatchNo || '-' }}</text>
						</view>
						<view class="transaction-quantities">
							<text class="transaction-quantity">
								数量：{{ formatQuantity(transaction.quantity) }} {{ getUnitName(transaction.quantityUnit) }}
							</text>
							<text v-if="transaction.lockQuantity" class="lock-quantity">
								锁定：{{ formatQuantity(transaction.lockQuantity) }} {{ getUnitName(transaction.quantityUnit) }}
							</text>
						</view>
						<view class="transaction-before-after">
							<text class="before-quantity">
								出入库前：{{ formatQuantity(transaction.beforeQuantity) }} {{ getUnitName(transaction.quantityUnit) }}
							</text>
							<text class="after-quantity">
								出入库后：{{ formatQuantity(transaction.afterQuantity) }} {{ getUnitName(transaction.quantityUnit) }}
							</text>
						</view>
						<view class="transaction-warehouse">
							<text class="from-warehouse">
								来源仓库：{{ transaction.fromWarehouseName || '-' }}
							</text>
							<text class="to-warehouse">
								目标仓库：{{ transaction.toWarehouseName || '-' }}
							</text>
						</view>
						<view v-if="transaction.remark" class="transaction-remark">
							<text class="remark-text">备注：{{ transaction.remark }}</text>
						</view>
					</view>
				</view>
				<view v-else class="no-data">
					<text>暂无交易明细</text>
				</view>
			</view>
		</scroll-view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<uni-load-more status="loading"></uni-load-more>
		</view>
	</view>
</template>

<script>
import { stockInfoApi } from '@/api/scm/inventory/stockinfo/index.js'
import { getBatchDictOptions, getDictLabel, DICT_TYPE } from '@/utils/dict.js'

export default {
	data() {
		return {
			stockId: null,
			loading: false,
			formData: null,
			transactionList: [],
			// 字典数据
			materialTypeOptions: [],
			materialSourceOptions: [],
			statusOptions: [],
			unitOptions: [],
			dictData: {}
		}
	},
	onLoad(options) {
		if (options.id) {
			this.stockId = options.id
			this.initData()
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 初始化数据
		async initData() {
			await this.getDictData()
			this.loadData()
		},

		// 获取字典数据
		async getDictData() {
			try {
				const dictTypes = [
					DICT_TYPE.MATERIAL_TYPE,
					DICT_TYPE.MATERIAL_SOURCE,
					DICT_TYPE.STOCK_STATUS,
					DICT_TYPE.SCM_BIZ_TYPE,
					DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION
				]
				this.dictData = await getBatchDictOptions(dictTypes)

				// 设置选项数据
				this.materialTypeOptions = this.dictData[DICT_TYPE.MATERIAL_TYPE] || []
				this.materialSourceOptions = this.dictData[DICT_TYPE.MATERIAL_SOURCE] || []
				this.statusOptions = this.dictData[DICT_TYPE.STOCK_STATUS] || []

			} catch (error) {
				console.error('获取字典数据失败:', error)
			}
		},

		// 加载数据
		async loadData() {
			if (!this.stockId) return

			this.loading = true
			try {
				// 加载库存信息
				const stockInfo = await stockInfoApi.getStockInfo(this.stockId)
				this.formData = stockInfo

				// 加载交易明细
				const transactions = await stockInfoApi.getTransactionListByInventoryId(this.stockId)
				this.transactionList = transactions || []

			} catch (error) {
				console.error('加载数据失败:', error)
				uni.showToast({
					title: '加载数据失败',
					icon: 'error'
				})
			} finally {
				this.loading = false
			}
		},

		// 保存数据
		async handleSave() {
			if (!this.formData) return

			try {
				uni.showLoading({ title: '保存中...' })
				
				await stockInfoApi.updateStockInfo(this.formData)
				
				uni.hideLoading()
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				})
				
				// 延迟返回上一页
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
				
			} catch (error) {
				uni.hideLoading()
				console.error('保存失败:', error)
				uni.showToast({
					title: '保存失败',
					icon: 'error'
				})
			}
		},

		// 格式化数量
		formatQuantity(value) {
			if (value === null || value === undefined || value === '') return '0'
			if (isNaN(value)) return '0'
			return Number(value).toLocaleString()
		},

		// 格式化日期
		formatDate(timestamp) {
			if (!timestamp) return '-'
			const date = new Date(timestamp)
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
		},

		// 获取单位名称
		getUnitName(unitId) {
			if (!unitId && unitId !== 0) return ''
			if (!Array.isArray(this.unitOptions)) return unitId.toString()
			const unit = this.unitOptions.find(item => item.id === unitId || item.value === unitId)
			return unit ? unit.name || unit.label : unitId.toString()
		},

		// 获取物料类型文本
		getMaterialTypeText(type) {
			if (!type) return '-'
			const option = this.materialTypeOptions.find(item => item.value === type)
			return option ? option.label : type
		},

		// 获取物料来源文本
		getMaterialSourceText(source) {
			if (!source) return '-'
			const option = this.materialSourceOptions.find(item => item.value === source)
			return option ? option.label : source
		},

		// 获取交易类型文本
		getTransactionTypeText(type) {
			if (!type) return '-'
			if (this.dictData.SCM_BIZ_TYPE) {
				const option = this.dictData.SCM_BIZ_TYPE.find(item => item.value === type)
				return option ? option.label : type
			}
			return type
		},

		// 获取交易方向文本
		getTransactionDirectionText(direction) {
			if (!direction) return '-'
			if (this.dictData.INVENTORY_TRANSACTION_DIRECTION) {
				const option = this.dictData.INVENTORY_TRANSACTION_DIRECTION.find(item => item.value === direction)
				return option ? option.label : direction
			}
			return direction
		}
	}
}
</script>

<style lang="scss" scoped>
.edit-container {
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.top-nav {
	background-color: white;
	padding: 20rpx 32rpx;
	border-bottom: 1px solid #eee;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: sticky;
	top: 0;
	z-index: 100;

	.nav-left {
		display: flex;
		align-items: center;
		gap: 16rpx;

		.nav-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}
	}

	.nav-right {
		.save-btn {
			font-size: 28rpx;
			color: #007AFF;
			font-weight: 500;
		}
	}
}

.form-content {
	flex: 1;
	padding: 24rpx;
}

.form-section {
	background-color: white;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	overflow: hidden;

	.section-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		padding: 32rpx 32rpx 24rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.form-item {
		display: flex;
		align-items: center;
		padding: 24rpx 32rpx;
		border-bottom: 1px solid #f8f9fa;

		&:last-child {
			border-bottom: none;
		}

		.label {
			font-size: 28rpx;
			color: #666;
			width: 200rpx;
			flex-shrink: 0;
		}

		.value {
			font-size: 28rpx;
			color: #333;
			flex: 1;
		}

		.input {
			flex: 1;
			font-size: 28rpx;
			color: #333;
			padding: 16rpx 20rpx;
			background-color: #f8f9fa;
			border-radius: 8rpx;
			border: 1px solid #e5e5e5;

			&:focus {
				border-color: #007AFF;
				background-color: white;
			}
		}

		.textarea {
			flex: 1;
			font-size: 28rpx;
			color: #333;
			padding: 16rpx 20rpx;
			background-color: #f8f9fa;
			border-radius: 8rpx;
			border: 1px solid #e5e5e5;
			min-height: 120rpx;

			&:focus {
				border-color: #007AFF;
				background-color: white;
			}
		}
	}
}

.transaction-list {
	.transaction-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin: 16rpx 32rpx;
		border: 1px solid #e5e5e5;

		&:last-child {
			margin-bottom: 32rpx;
		}

		.transaction-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding-bottom: 12rpx;
			border-bottom: 1px solid #f0f0f0;

			.transaction-type {
				font-size: 28rpx;
				font-weight: 600;
				color: #333;
			}

			.transaction-date {
				font-size: 24rpx;
				color: #666;
			}
		}

		.transaction-details {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 12rpx 0;

			.transaction-no {
				font-size: 24rpx;
				color: #666;
			}

			.transaction-batch {
				font-size: 24rpx;
				color: #666;
			}
		}

		.transaction-quantities {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 12rpx 0;

			.transaction-quantity {
				font-size: 24rpx;
				color: #666;
			}

			.lock-quantity {
				font-size: 24rpx;
				color: #666;
			}
		}

		.transaction-before-after {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 12rpx 0;

			.before-quantity {
				font-size: 24rpx;
				color: #666;
			}

			.after-quantity {
				font-size: 24rpx;
				color: #666;
			}
		}

		.transaction-warehouse {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 12rpx 0;

			.from-warehouse {
				font-size: 24rpx;
				color: #666;
			}

			.to-warehouse {
				font-size: 24rpx;
				color: #666;
			}
		}

		.transaction-remark {
			margin-top: 12rpx;
			padding-top: 12rpx;
			border-top: 1px solid #f0f0f0;

			.remark-text {
				font-size: 24rpx;
				color: #666;
				line-height: 1.5;
			}
		}
	}
}

.no-data {
	text-align: center;
	padding: 80rpx 32rpx;
	color: #999;
	font-size: 28rpx;
}

.loading-container {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
</style>
