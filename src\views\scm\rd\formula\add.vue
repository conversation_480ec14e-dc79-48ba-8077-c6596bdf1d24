<template>
  <div>
    <el-tabs v-model="activeTab" v-show="formulaId !== null || nowPage === 'formula'">
      <el-tab-pane label="基本信息" name="basic">
        <el-form
          ref="formulaRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          :disabled="isDisabled"
        >
          <el-form-item label="配方名称" prop="name">
            <el-space :size="10">
              <el-input
                v-model="form.name"
                placeholder="请输入配方名称"
                style="width: 650px"
                :disabled="isNameInputDisabled"
              >
                <template #append> 序号(自动生成) </template>
              </el-input>
              <el-link type="primary" @click="customizeName">{{
                customizeButtonName
              }}</el-link>
            </el-space>
          </el-form-item>
          <el-form-item label="产品形态" prop="stateCode">
            <el-select
              v-model="form.stateCode"
              placeholder="形态"
              style="width: 80px"
              @change="changeName"
              clearable
              filterable
            >
              <el-option
                v-for="dict in product_state"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="通用名" prop="typeCode">
            <el-select
              v-model="form.typeCode"
              placeholder="请选择通用名"
              style="width: 190px"
              @change="changeName"
              clearable
              filterable
            >
              <el-option
                v-for="dict in product_category"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="企业" prop="companyName">
            <el-space :size="10">
              <el-select
               v-model="form.companyName" 
               placeholder="请选择企业" 
               style="width: 190px" 
               @change="changeCompany" 
               clearable 
               filterable 
               remote 
               :remote-method="searchCompany" 
               :reserve-keyword="true" 
               >
                <div v-infinite-scroll="scrollLoad" style="max-height: 200px;overflow: auto;">
                  <el-option
                    v-for="companyItem in companysOptions"
                    :key="companyItem.id"
                    :label="companyItem.name"
                    :value="companyItem.name"
                  >               <div class="company-option">
                    <div>{{companyItem.shortName}} || {{companyItem.name }}</div>
                      <p>{{ companyItem.code }}</p>
                    </div>
                  </el-option>
                </div>
                <template #empty>
                  <p class="add-company" @click="toAddCompany" v-hasPermi="['base:company:create']">
                    <i class="fas fa-add"></i>增加企业
                  </p>
                </template>
              </el-select>
              <el-link type="primary" @click="viewHistory"
                >查看历史配方</el-link
              >
            </el-space>
          </el-form-item>
          <el-form-item label="氮磷钾含量" prop="npk_element">
            <el-space :size="10">
              <el-input
                v-model="form.npk.N"
                type="number"
                style="max-width: 250px"
                placeholder="输入氮含量"
                @change="changeName"
                class="npk-input"
              >
              <template #prepend>N</template>
                <template #append>
                  <el-select v-model="form.npk.n_unit" placeholder="请选择单位" style="width: 120px;">
                    <el-option v-for="dict in quality_unit" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>                
                </template>
              </el-input>
              <span>-</span>
              <el-input
                v-model="form.npk.P"
                type="number"
                style="max-width: 350px"
                placeholder="输入磷含量"
                @change="changeName"
                class="npk-input"
              >
                <template #prepend>
                  <el-select
                    v-model="form.npk.PType"
                    placeholder="请选择磷原料类型"
                    style="width: 120px"
                    @change="changeName"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="dict in k_elements"
                      :key="dict.value"
                      :label="dict.name"
                      :value="dict.value"/>
                  </el-select>
                </template>
                <template #append>
                  <el-select v-model="form.npk.p_unit" placeholder="请选择单位" style="width: 120px;">
                    <el-option v-for="dict in quality_unit" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </template>
              </el-input>
              <span>-</span>
              <el-input
                v-model="form.npk.K"
                type="number"
                style="max-width: 250px"
                placeholder="输入钾含量"
                @change="changeName"
                class="npk-input"
              >
              <template #prepend>K</template>
                <template #append>            
                   <el-select v-model="form.npk.k_unit" placeholder="请选择单位" style="width: 120px;">
                    <el-option v-for="dict in quality_unit" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>     
                </template>
              </el-input>
            </el-space>
          </el-form-item>

          <el-form-item label="中微量元素含量" prop="microElement">
            <el-space direction="vertical">
              <el-space direction="horizontal" v-for="(item, index) in form.microElement"  :key="`${item.element}-${index}`" >
                <el-input
                  type="number"
                  min="0"
                  v-model="item.quantity"
                  placeholder="输入含量"
                  @change="changeName"
                  input-style="text-align: center;max-width:200px"
                >
                  <template #prepend>
                    <el-select
                      v-model="item.element"
                      placeholder="请选择元素"
                      style="width: 120px"
                      @change="changeName"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="dict in medium_trace_element"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        :disabled="isMicroElementSelected(dict.value)"
                      />
                    </el-select>
                  </template>
                  <template #append>
                    <el-select
                      v-model="item.unit"
                      placeholder="请选择单位"
                      style="width: 120px"
                      @change="changeName"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="dict in quality_unit"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </template>
                </el-input>
                <el-button
                  link
                  type="primary"
                  @click="addMicroElement"
                  v-hasPermi="['quote:formula:update']"
                ><Icon icon="ep:plus"/></el-button>
                <el-button
                  link
                  type="danger"
                  @click="removeMicroElement(index)"
                  v-hasPermi="['quote:formula:update']"
                ><Icon icon="ep:delete"/></el-button>
              </el-space>
            </el-space>
          </el-form-item>
          <el-form-item label="其他原料" prop="otherMaterial">
            <el-space direction="vertical">
              <el-space
                  direction="horizontal"
                  v-for="(item, index) in form.otherMaterial || []"
                  :key="`${item.element}-${index}`" 
                >
                <el-input
                  type="number"
                  min="0"
                  v-model="item.quantity"
                  placeholder="输入含量"
                  @change="changeName"
                  input-style="text-align: center;max-width:200px"
                >
                  <template #prepend>
                    <el-select
                      v-model="item.element"
                      placeholder="请选择其他原料"
                      style="width: 120px"
                      @change="changeName"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="dict in product_name_abbr"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        :disabled="isOtherMaterialSelected(dict.value)"
                      />
                      <template #footer>
                        <div class="add-material-btn" @click="showAddMaterialDialog">
                          <Icon icon="ep:plus" class="mr-5px" />
                          添加新原料
                        </div>
                      </template>
                    </el-select>
                  </template>
                  <template #append>
                    <el-select
                      v-model="item.unit"
                      placeholder="请选择单位"
                      style="width: 120px"
                      @change="changeName"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="dict in quality_unit"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </template>
                </el-input>
                <el-button
                  link
                  type="primary"
                  @click="addOtherElement"
                  v-hasPermi="['quote:formula:update']"
                ><Icon icon="ep:plus"/></el-button>
                <el-button
                  link
                  type="danger"
                  @click="removeOtherElement(index)"
                  v-hasPermi="['quote:formula:update']"
                ><Icon icon="ep:delete"/></el-button>
              </el-space>
            </el-space>
          </el-form-item>
          <el-form-item label="工艺说明" prop="processDesc">
            <el-input
              v-model="form.processDesc"
              type="textarea"
              placeholder="请输入内容"
              rows="4"
              autosize
            />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
              autosize
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="配方明细" name="detail">
        <el-table
          :data="tableData"
          border
          show-summary
          :summary-method="summaryMethod"
          @row-dblclick="addMaterial"
          max-height="400"
        >
          <!-- <el-table-column
            type="index"
            label="序号"
            width="80"
            align="center"
          /> -->
          <el-table-column label="序号" prop="num" align="center" width="180">
            <template #default="{row}">
              <el-input-number v-model="row.num" placeholder="顺序" :min="1" :max="100" @change="updateMaterial(row, $event)" controls-position="right" :disabled="isDisabled"/>
            </template>
          </el-table-column>
          <el-table-column
            prop="materialName"
            label="原料名称"
            align="center"
            width="220"
          >
            <template #default="{ row }">
              <el-select 
              v-model="row.materialName"
              placeholder="请选择原料"
              :disabled="isDisabled"
              @change="(value) => { updateMaterial(row, value,'material'); calculatePackageCosts(); }"
              :remote-method="searchMaterial"
              remote
              filterable
              >
                <div v-infinite-scroll="scrollLoad" style="max-height: 200px;overflow: auto;">
                  <el-option
                    v-for="material in materialOptions"
                    :key="material.id"
                    :label="material.name"
                    :value="material.name"
                    :disabled="materialIsReady(material.id)"
                  >
                    <div>
                      <span>{{ material.name }}</span>
                      <span style="margin-left: 10px; font-size: 12px; color: #8492a6">{{ material.code }}</span>
                    </div>
                    <template #footer>
                      <p class="loading-more-text"  v-if="materialsScrollLoading"><el-icon class="is-loading"><Loading /></el-icon>正在加载更多数据</p>
                    </template>
                  </el-option>
                </div>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            label="投入份数"
            prop="amount"
            align="center"
            width="180"
          >
            <template #default="{ row }">
              <el-input-number
                v-model="row.amount"
                :min="0"
                @change="calculateCosts"
                controls-position="right"
                style="width: 106px"
                :disabled="isDisabled"
              >
                <template #suffix>
                  <span>份</span>
                </template>
              </el-input-number>
            </template>
          </el-table-column>
          <el-table-column
            v-for="ele in displayedElements"
            :key="ele"
            :prop="ele"
            :label="ele"
            align="center"
          >
            <template #default="{ row }">{{
              (row.element && row.element[ele]) || 0
            }}</template>
          </el-table-column>
          <el-table-column label="单价" prop="price" align="center">
            <template #default="{ row }"
              >{{ row.price }} {{ row.priceUnit }}</template
            >
          </el-table-column>
          <el-table-column label="投入成本" prop="cost" align="center" width="150">
            <template #default="{ row }">{{ row.cost }} {{row.priceUnit}}</template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="180"
            v-hasPermi="['quote:formula:update']"
            align="center"
            fixed="right"
          >
            <template #default="{ row }">
              <el-button
link type="primary" @click="addMaterial"
                :disabled="isDisabled"
                >增加</el-button
              >
              <el-button
                link
                type="danger"
                @click="removeMaterial(row)"
                :disabled="isDisabled"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

 
      </el-tab-pane>
      <el-tab-pane label="包装明细" name="package" v-if="nowPage !== 'formula' && operate !== 'designFormula'">
            <el-table
              :data="packageData"
              border
              show-summary
              :summary-method="summaryPackageMethod"
              @row-dblclick="addPackage"
              max-height="400"
            >
          <el-table-column
            type="index"
            label="序号"
            width="80"
            align="center"
          />
          <el-table-column label="顺序" prop="num" align="center" width="180">
            <template #default="{row}">
              <el-input-number v-model="row.num" placeholder="顺序" :min="1" :max="100" @change="updatePackage(row, $event)" controls-position="right" :disabled="isDisabled"/>
            </template>
          </el-table-column>
          <el-table-column
            prop="materialName"
            label="包装材料名称"
            align="center"
            width="220"
          >
            <template #default="{ row }">
              <el-select 
              v-model="row.materialName"
              placeholder="请选择包装材料"
              :disabled="isDisabled"
              @change="(value) => { updateMaterial(row, value,'package'); calculatePackageCosts(); }"
              :remote-method="searchMaterial"
              remote
              filterable
              >
                <div v-infinite-scroll="scrollLoad" style="max-height: 200px;overflow: auto;" class="infinite-list">
                  <el-option
                    v-for="material in packagesOptions"
                    :key="material.id"
                    :label="material.name"
                    :value="material.name"
                    :disabled="packageIsReady(material.id)"
                  >
                    <div>
                      <span>{{ material.name }}</span>
                      <span style="margin-left: 10px; font-size: 12px; color: #8492a6">{{ material.code }}</span>
                    </div>
                  </el-option>
                </div>
                <template #footer>
                  <p class="search-more-text" @click="searchPackageMaterial">搜索</p>
                </template>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            label="数量"
            prop="amount"
            align="center"
            width="180"
          >
            <template #default="{ row }">
              <el-input-number
                v-model="row.amount"
                :min="0"
                @change="calculatePackageCosts"
                controls-position="right"
                style="width: 106px"
                :disabled="isDisabled"
              >
                <template #suffix>
                  <span>个</span>
                </template>
              </el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="单价" prop="price" align="center">
            <template #default="{ row }"
              >{{ row.price }} {{ row.priceUnit }}</template
            >
          </el-table-column>
          <el-table-column label="成本" prop="cost" align="center" width="150">
            <template #default="{ row }">{{ row.cost }} {{ row.priceUnit }}</template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="180"
            v-hasPermi="['quote:formula:update']"
            align="center"
            fixed="right"
          >
            <template #default="{ row }">
              <el-button 
              link 
              type="primary" 
              @click="addPackage"
              :disabled="isDisabled"
              >
              增加
              </el-button>
              <el-button
                link
                type="danger"
                @click="removePackage(row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-divider content-position="left" v-if="activeTab !== 'basic'"><el-text class="mx-1" type="primary">其他信息</el-text></el-divider
        >
        <el-form
          ref="formulaRef"
          :model="form"
          :rules="rules"
          :inline="true"
          size="default"
          label-width="auto"
          :disabled="isDisabled"
          v-if="activeTab !== 'basic'"
          class="other-info-form"
        >
          <el-form-item label="投入总份数" prop="totalAmount" class="form-item-large">
            <el-input
              v-model="form.totalAmount"
              placeholder="请输入投入总份数"
              disabled
            />
          </el-form-item>
          <el-form-item label="密度" prop="density" class="form-item-medium">
            <el-input-number
              v-model="form.density"
              placeholder="请输入密度"
              min="0"
              @tab-click="handleClick"
              :step="0.01"
            />
          </el-form-item>
          <el-form-item label="PH值" prop="ph" class="form-item-medium">
            <el-input
              v-model="form.ph"
              placeholder="请输入PH"
            />
          </el-form-item>
          <el-form-item label="EC" prop="ec" class="form-item-medium">
            <el-input v-model="form.ec" placeholder="请输入EC" />
          </el-form-item>
          <el-form-item label="总成本" prop="totalCost" class="form-item-large">
            <el-input
              v-model="form.totalCost"
              placeholder="请输入总成本"
              type="number"
              disabled
            >
              <template #suffix>
                <span>元</span>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="总原料成本" prop="totalRawCost" class="form-item-medium">
            <el-input
              v-model="form.totalRawCost"
              placeholder="请输入总原料成本"
              disabled
              type="number"
            >
              <template #suffix>
                <span>元</span>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="元素含量" prop="elements" v-if="safeElements" class="form-item-medium">
            <el-space spacer="|" wrap>
              <div v-for="key in Object.keys(safeElements)" :key="key">
                <el-input type="number" v-model="safeElements[key]" disabled input-style="text-align: right;" class="element-input">
                  <template #prefix>{{ key }}:</template>
                  <template #suffix>%</template>
                </el-input>
              </div>
            </el-space>
          </el-form-item>
          <el-form-item label="水溶性" prop="waterSoluble" class="form-item-large">
            <el-input v-model="form.waterSoluble" placeholder="请输入水溶性"/>
          </el-form-item>
          <el-form-item label="包装成本" prop="packaging" v-if="nowPage === 'apply'" class="form-item-large">
            <el-input
              v-model="packageCost"
              placeholder="请输入包装成本"
              type="number"
              @change="updatePackageCost"
            >
              <template #suffix>
                <span>元</span>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="封装成本" prop="totalPackageCost" v-if="nowPage === 'apply'" class="form-item-large">
            <el-input
              :model-value="form.totalPackageCost"
              placeholder="请输入封装成本"
              type="number"
              @change="calculateTotalCosts"
            >
              <template #suffix>
                <span>元</span>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="外观" prop="appearance" class="form-item-full">
            <el-input v-model="form.appearance" placeholder="请输入外观"/>
          </el-form-item>
          <el-form-item label="工艺说明" prop="processDesc" class="form-item-full">
            <el-input
              v-model="form.processDesc"
              type="textarea"
              placeholder="请输入内容"
              rows="4"
              autosize
            />
          </el-form-item>
          <el-form-item label="备注" prop="remark" class="form-item-full">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
              autosize
            />
          </el-form-item>
        </el-form>
    </el-tabs>
    <!-- 将历史配方作为单独的对话框而不是表单项 -->
    <el-dialog
      v-model="historyOpen"
      title="历史配方列表"
      :before-close="closeHistory"
      append-to-body
    >
      <formula-table 
        ref="historyRef"
        :showSelection="false"
        :size="'small'"
        :isSmallPagination="true"
        :externalParams="{ companyName: form.companyName }"
        :autoLoad="false"
        :showOperations="false"
        @select="selectHistoryFormula"
        @row-click="selectHistoryFormula"
      />
    </el-dialog>
    <el-dialog title="管理企业" v-model="openSupplier" width="600px" append-to-body fullscreen>
      <component
        :is="company"
        :company="addCompanyForm"
        :openAdd="openAdd"
        :key="openSupplier"
        @close="handleCloseSupplier"
      />
    </el-dialog>
    <el-dialog title="搜索包装材料" v-model="openSearchPackage" width="900px" append-to-body style="max-height: 800px;">
      <search-package-form ref="searchPackageFormRef"/>
      <el-divider />
      <template #footer>
        <el-button type="primary" @click="handleConfirmSearchPackage">确定</el-button>
        <el-button @click="openSearchPackage = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 添加新原料对话框 -->
    <el-dialog title="添加新原料" v-model="showAddMaterialDialogVisible" width="400px" append-to-body>
      <div style="padding-top: 20px;">
        <el-form :model="newMaterialForm" label-width="80px">
          <el-form-item label="原料名称" required>
            <el-input
              v-model="newMaterialForm.name"
              placeholder="请输入原料名称"
              @keyup.enter="handleAddNewMaterial"
            />
          </el-form-item>
          <el-form-item label="原料代码" required>
            <el-input
              v-model="newMaterialForm.value"
              placeholder="请输入原料代码"
              @keyup.enter="handleAddNewMaterial"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showAddMaterialDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAddNewMaterial" :loading="addMaterialLoading">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="AddFormula">
import {
  FormulaApi
} from "@/api/scm/rd/formula";
import {
  ApplyApi
} from "@/api/scm/quote/apply";
import { MaterialApi } from "@/api/scm/base/material";
import { CompanyApi} from "@/api/scm/base/company";
import SearchPackageForm from './SearchPackageForm.vue'
import { FormulaDetailApi } from '@/api/scm/rd/formuladetail'; // 请检查路径是否准确
import {
  ref,
  watch,
  computed,
  defineExpose,
  onMounted,
  defineEmits,
  onUnmounted,
  inject,
  reactive, 
  toRefs,   
  nextTick 
} from "vue";
import FormulaTable from "@/views/scm/rd/formula/history.vue";
import FormDialog from "@/components/CommonDialog/FormDialog.vue";
import { markRaw } from "vue";
import { createDictData } from "@/api/system/dict/dict.data.ts";
import { ElMessage, ElMessageBox } from "element-plus";
import { Loading } from '@element-plus/icons-vue'; // 导入Loading图标
import { checkPermi } from "@/utils/permission"; // 假设权限检查函数在这里
import ExcelJS from 'exceljs';
import company from '../../base/company/index.vue'
import { DICT_TYPE, getDictOptions } from "@/utils/dict";
import { ElLoading } from 'element-plus'; // 导入 ElLoading 组件
import { debounce } from "min-dash";

const product_category = getDictOptions(DICT_TYPE.PRODUCT_CATEGORY)
const product_state =  getDictOptions(DICT_TYPE.PRODUCT_STATE)
const product_name_abbr =  ref(getDictOptions(DICT_TYPE.PRODUCT_NAME_ABBR)) // 使用 ref
const quality_unit =  getDictOptions(DICT_TYPE.QUALITY_UNIT)
const medium_trace_element =  getDictOptions(DICT_TYPE.MEDIUM_TRACE_ELEMENT)
const cost_types =  getDictOptions(DICT_TYPE.COST_TYPES)
const message = useMessage() // 消息弹窗

const initialMaterials = ref([]);
const initialPackages = ref([]); 

const VITE_GLOB_API_URL = import.meta.env.VITE_GLOB_API_URL;
const areObjectsDifferent = (obj1, obj2, keysToCompare) => {
  if (!obj1 || !obj2) return true; // 如果一个为空，则认为不同或需要特殊处理
  for (const key of keysToCompare) {
    const val1 = obj1[key];
    const val2 = obj2[key];
    // 简单比较，对于对象或数组可能需要深比较或特定逻辑
    if (typeof val1 === 'object' && val1 !== null && typeof val2 === 'object' && val2 !== null) {
      // 如果是element对象，特殊处理，确保empty标志也被比较
      if (key === 'element') {
        const e1 = { ...val1 };
        const e2 = { ...val2 };
        if (Object.keys(e1).length === 0 || (Object.keys(e1).length === 1 && e1.hasOwnProperty('empty'))) e1.empty = true;
        if (Object.keys(e2).length === 0 || (Object.keys(e2).length === 1 && e2.hasOwnProperty('empty'))) e2.empty = true;
        if (JSON.stringify(e1) !== JSON.stringify(e2)) return true;
      } else if (JSON.stringify(val1) !== JSON.stringify(val2)) {
        return true;
      }
    } else if (val1 !== val2) {
      return true;
    }
  }
  return false;
};

const formulaRef = ref(null);
const open = ref(false);
const defaultRatio = ref(1000);
const isNameInputDisabled = ref(true);
const customizeButtonName = ref("自定义名称");
const packageCost = ref(0); // 新增包装成本变量
const openSupplier = ref(false)
const historyOpen = ref(false);
const historyRef = ref(null);
const openAdd = ref(true)

// *** 注入父组件提供的 form ***
const quoteForm = inject('quoteForm', ref({})); // 注入名为 'quoteForm' 的 ref
const formulaDataOK = ref(false);
const operateType = ref("add");
const btnName = ref("添加");

const isDisabled = ref(false); 
const form = ref({})

const props = defineProps({
  formulaId: {
    type: Number,
    default: null,
  },
  applyId: {
    type: Number,
    default: null,
  },
  refresh: {
    type: Boolean,
    default: false,
  },
  operate: {
    type: String,
    default: "addFormula",
  },
  producerName: {
    type: String,
    default: null,
  },
  producerId: {
    type: Number,
    default: null,
  },
  nowPage: {
    type: String,
    default: null,
  },
  applyStatus: {
    type: Number,
    default: null
  }
});
const addCompanyForm = ref({
  name:form.value.companyName,
  isProducer:0,
  isSupplier:0,
  isCustomer:1
})
watch(() => props.applyStatus,(newVal) => { 
  if(props.nowPage !== 'formula'){
      if(newVal === null || newVal < 4){ 
      isDisabled.value = true
    }else{
      isDisabled.value = false 
    }
  }
}, { immediate: true });

// 新增：临时存储顶部表单数据的变量
const topFormBackup = ref(null);

// 新增：在子组件初始化时备份顶部表单数据的函数
const backupTopFormData = () => {
  // 只保存关键字段而不是整个表单对象，避免引用问题
  if (form.value) {
    topFormBackup.value = {
      customerCode: form.value.customerCode,
      customerName: form.value.customerName,
      producerId: form.value.producerId,
      producerName: form.value.producerName,
      productName: form.value.productName,
      requirement: form.value.requirement,
      spec: form.value.spec,
      amount: form.value.amount,
      unit: form.value.unit,
      id: form.value.id,
      status: form.value.status,
      approveDesc: form.value.approveDesc,
      remark: form.value.remark
    };
  }
};

const handleConfirmSearchPackage = () => {
  if (searchPackageFormRef.value && typeof searchPackageFormRef.value.getSelectedPackages === 'function') {
    const selectedPkgs = searchPackageFormRef.value.getSelectedPackages();
    if (selectedPkgs && selectedPkgs.length > 0) {
      form.value.packages = form.value.packages.filter(item => {
        return item.materialId !== null
      })
      selectedPkgs.forEach((pkg, index) => {
        addPackage(pkg)
      });
      // 更新包装相关的成本计算
      calculatePackageCosts();
    }
  }
  openSearchPackage.value = false; // 关闭对话框
};

// 新增：在子组件操作完成后恢复顶部表单数据的函数
const restoreTopFormData = () => {
  if (topFormBackup.value && form.value) {
    // 恢复已保存的顶部表单字段
    Object.keys(topFormBackup.value).forEach(key => {
      if (topFormBackup.value[key] !== undefined) {
        form.value[key] = topFormBackup.value[key];
      }
    });
  }
};

// 在组件挂载后备份顶部表单数据
onMounted(() => {
  // 延迟执行备份，确保数据已加载
  setTimeout(() => {
    backupTopFormData();
  }, 500);
});

// 监听表单变化，在表单数据重置后尝试恢复顶部字段
watch(form, () => {
  // 禁用自动恢复功能，在修改模式下不应自动恢复表单数据
  // 如果存在表单ID，说明是修改模式，此时不应该自动恢复数据
  if (form.value && form.value.id) {
    return; // 在修改模式下，退出监听器，不执行自动恢复
  }
  
  // 只在新增模式下才尝试恢复数据
  if (form.value && 
      (!form.value.customerName || !form.value.producerName || !form.value.requirement) && 
      topFormBackup.value && 
      (topFormBackup.value.customerName || topFormBackup.value.producerName || topFormBackup.value.requirement)) {
    restoreTopFormData();
  }
}, { deep: true });

// 确保 data 中不再包含 form
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    type: null,
    applyId: null,
    totalAmount: null,
    elements: null,
    totalCost: null,
    totalRawCost: null,
    totalAuxiliaryCost: null,
    processDesc: null,
    status: null,
  },
  rules: {
    name: [{ required: true, message: "配方名称不能为空", trigger: "blur" }],
    totalCost: [{ required: true, message: "总成本不能为空", trigger: "blur" }],
  },
  approveRules: {
  },
  costTypeForm: {
    name: "",
  },
  costTypeFormRules: {
    name: [
      { required: true, message: "成本类型名称不能为空", trigger: "blur" },
    ],
  },
  isLoading: false,
  apiData: null, // 重命名为 apiData
  error: null,
});

const { queryParams, rules } = toRefs(data);

const activeTab = ref("basic");
const viewHistory = () => {
  if (!form.value.companyName) {
    message.warning("请先选择企业");
    return;
  }
  historyOpen.value = true;
  // 等待组件挂载后刷新数据
  nextTick(() => {
    if (historyRef.value) {
      historyRef.value.queryParams.companyName = form.value.companyName;
      historyRef.value.getList();
    }
  });
};
// 选择历史配方
const selectHistoryFormula = (row) => {
  // 获取选中配方的详细数据
  FormulaApi.getFormula(row.id,true).then(response => {
    // 将历史配方数据复制到当前表单，但保留当前ID为null（创建新配方）
    const historyData = response.data;
    const currentName = form.value.name;
    
    // 复制数据到表单，但不包括ID（创建新配方）
    form.value = { ...historyData, id: null };
    
    // 如果是自定义名称模式，保留当前名称
    if (!isNameInputDisabled.value && currentName) {
      form.value.name = currentName;
    }
    
    // 确保其他必要的属性都存在
    if (!form.value.npk) form.value.npk = { N: null, P: null, PType: "P", K: null };
    if (!form.value.microElement || !Array.isArray(form.value.microElement) || form.value.microElement.length === 0) {
      form.value.microElement = [{ element: null, quantity: null, unit: null }];
    }
    if (!form.value.otherMaterial || !Array.isArray(form.value.otherMaterial)) {
      form.value.otherMaterial = [{ element: "", quantity: "0", unit: "元" }];
    }
    if (!form.value.costItems || !Array.isArray(form.value.costItems)) {
      form.value.costItems = [{ element: "", quantity: "0", unit: "元" }];
    }
    
    // 触发计算，确保所有依赖的计算属性都被更新
    calculateCosts();
    calculateTotalCosts();
    
    // 关闭历史配方对话框
    historyOpen.value = false;
    
    // 提示成功信息
    message.success("已从历史配方创建新配方");

    // +++ 更新快照 +++
    nextTick(() => {
      initialMaterials.value = JSON.parse(JSON.stringify(form.value.materials || []));
      initialPackages.value = JSON.parse(JSON.stringify(form.value.packages || []));
      initialFormSnapshot.value = JSON.stringify(form.value);
    });

  }).catch(error => {
    message.error("获取历史配方数据失败: " + (error.message || "未知错误"));
  });
};

// 添加监听器监听公司名称变化
watch(
  () => form.value.companyName,
  (newVal) => {
    if (historyOpen.value && historyRef.value) {
      historyRef.value.queryParams.companyName = newVal;
      historyRef.value.getList();
    }
  }
);

const k_elements = ref([
  { code: "P", name: "磷", value: "P" },
  { code: "KDP", name: "磷酸二氢钾", value: "KDP" },
  { code: "BP", name: "膨化磷酸二氢钾", value: "BP" },
  { code: "NOP", name: "硝酸钾", value: "NOP" },
]);

const defaultTableElements = ref({
  num: 1,
  formulaId: "",
  materialId: null, // 默认原料名称为空，需要用户选择
  materialCode: "",
  materialName: "",
  amount: 0, // 默认投入份数为0
  price: 0, // 默认单价为0
  unit: "",
  materialType: "",
  element: {},
  cost: 0, // 默认投入成本为0
});

// 添加一个状态变量跟踪组件是否已经初始挂载
const hasInitialized = ref(false);
const isLoading = ref(false);

// +++ 新增：用于存储表单初始状态的快照 +++
const initialFormSnapshot = ref(JSON.stringify({})); // 初始化为一个空对象的字符串快照
const initialMaterialsForDirtyCheck = ref([]);
const initialPackagesForDirtyCheck = ref([]);
const openSearchPackage = ref(false)
const searchPackageFormRef = ref(null); // 新增 ref
const searchPackageMaterial = () => {
  openSearchPackage.value = true;
}

// 添加新原料相关状态
const showAddMaterialDialogVisible = ref(false)
const addMaterialLoading = ref(false)
const newMaterialForm = ref({
  name: '',
  value: ''
})
// 表单重置
function reset() {

  // 在重置表单之前先备份顶部表单数据
  backupTopFormData();

  // 在重置表单之前先标记为不处于加载状态
  isLoading.value = false;
  
  // 重置包装成本变量
  packageCost.value = 0;

  initialMaterials.value = []; // 清空
  initialPackages.value = [];  // 清空

  // 确保默认表单元素已定义
  const emptyMaterial = {
    num: 1,
    formulaId: "",
    materialId: null,
    materialCode: "",
    materialName: "",
    amount: 0,
    price: 0,
    unit: "",
    materialType: "",
    element: {},
    cost: 0,
  };

  // 保存顶部表单的重要字段
  const savedFields = {};
  const keysToPreserve = [
    'customerCode', 'customerName', 'producerId', 'producerName',
    'productName', 'requirement', 'spec', 'amount', 'unit',
    'id', 'applyId', 'status', 'approveDesc', 'remark', 'companyId',
    // 新增以下字段，确保它们在子组件重置时被保留，因为它们由父组件管理
    'formulaName', 'selectedFormula'
  ];
  
  keysToPreserve.forEach(key => {
    if (form.value && form.value[key] !== undefined) {
      savedFields[key] = form.value[key];
    }
  });

  // 清除配方相关字段但不影响顶部表单字段
  // 只对子组件负责的字段进行重置
  const formulaFields = {
    name: null,
    typeCode: null,
    nameRule: null,
    companyName: form.value?.companyName || null, // 保留公司名称
    companyCode: null,
    stateCode: null,
    npk: {
      N: null,
      P: null,
      PType: "P",
      K: null,
      n_unit: '%',
      k_unit: '%',
      p_unit:'%'
    },
    microElement: [{ element: null, quantity: null, unit: null }],
    otherMaterial: [{ element: "", quantity: "0", unit: "" }],
    otherMaterialQuantity: null,
    otherMaterialUnit: null,
    elements: {},
    totalAmount: 0,
    totalCost: 0,
    totalRawCost: 0,
    totalAuxiliaryCost: 0,
    totalPackageCost: 0,
    processDesc: null,
    materials: Array.from({ length: 13 }, (_, index) => ({
      ...emptyMaterial,
      num: index + 1
    })), // 新建配方时默认提供13行
    packages: [{ 
      num: 1,
      formulaId: "",
      materialId: null,
      materialCode: "",
      materialName: "",
      amount: null,
      price: 0,
      unit: "元",
      materialType: "4",
      element: {},
      cost: 0 
    }],
    materialsSum: null,
    density: 1,
    ph: null,
    costItems: [{ element: "", quantity: "0", unit: "元" }],
  };

  // 合并保存的顶部表单字段和配方字段
  Object.assign(form.value, formulaFields);
  
  // 恢复保存的顶部表单字段
  Object.assign(form.value, savedFields);

  // 重置状态变量（但不触发数据加载）
  isNameInputDisabled.value = true;
  customizeButtonName.value = "自定义名称";

  // 重置tab为基本信息
  activeTab.value = "basic";

  // 使用nextTick确保DOM更新完成
  nextTick(() => {
    // 确保包装数据始终初始化
    ensurePackageData();

    // +++ 更新快照 +++
    initialMaterials.value = JSON.parse(JSON.stringify(form.value.materials || []));
    initialPackages.value = JSON.parse(JSON.stringify(form.value.packages || []));
    initialFormSnapshot.value = JSON.stringify(form.value);
  });
}
const packagesScrollLoading = ref(false)
const packagePageNo = ref(1)
const materialPageNo = ref(1)
const materialScrollLoading = ref(false)
const scrollLoad = debounce(async () => {
  materialScrollLoading.value = true
  packagesScrollLoading.value = true
  let pageNo = 1
  if(activeTab.value === 'detail'){
      materialPageNo.value++
      pageNo = materialPageNo.value
    const response = await MaterialApi.getSimpleMaterialPage({
      pageNo:pageNo,
      pageSize:10,
      type:1,
      name:searchMaterialName.value
    })
    materialOptions.value.push(...response.list)
    materialScrollLoading.value = false

  }else if(activeTab.value === 'basic'){
    companyPageNum.value++
    pageNo = companyPageNum.value
    const response = await CompanyApi.getCompanyPage({
      pageNo:pageNo,
      pageSize:10,
    })
    companys.value.push(...response.list)
  }else if(activeTab.value === 'package'){
    packagePageNo.value++
    pageNo = packagePageNo.value
    const response = await MaterialApi.getSimpleMaterialPage({
      pageNo:pageNo,
      pageSize:10,
      type:4,
      name:searchMaterialName.value
    })
    packagesOptions.value.push(...response.list)
    packagesScrollLoading.value = false
  }
})

// 添加一个明确的清理方法，用于完全重置组件状态
function clear() {
    // 备份顶部表单数据
    backupTopFormData();
    
    // 原有的重置逻辑 - 但不清空整个表单，只清理子组件管理的部分
    isLoading.value = false;
    
    initialMaterials.value = [];
    initialPackages.value = []; 
    
    // 重置子组件的数据表
    tableData.value.materialData = [];
    tableData.value.packageData = [];
    tableData.value.auxiliaryData = [];

    // 重置成本相关数据
    form.value.totalRawCost = 0;
    form.value.totalAuxiliaryCost = 0;
    packageCost.value = 0;
    form.value.costSum = 0;
    
    // 重置配方相关字段，但保留表单顶部字段
    const configFields = {
      name: null,
      stateCode: null,
      companyName:  null,
      typeCode: null,
      companyId: null,
      nameRule: null,
      rootId: null, 
      sourceId: null,
      version: null, 
      npk: {
        N: null,
        P: null,
        K: null,
        PType: "P",
        n_unit: '%', 
        p_unit: '%', 
        k_unit: '%'
      },
      totalAmount: 0,
      totalCost: 0,
      totalRawCost: 0,
      totalAuxiliaryCost: 0,
      totalPackageCost: 0,
      elements: {},
      density: 1,
      ph: null, 
      ec: null,
      waterSoluble: null,
      appearance: null, 
      remark: null,
      processDesc: null, 
      materials: Array.from({ length: 13 }, (_, index) => ({
        ...defaultTableElements.value,
        num: index + 1
      })),
      packages: [{ 
        num: 1,
        formulaId: "",
        materialId: null,
        materialCode: "",
        materialName: "",
        amount: null,
        price: 0, 
        unit: "元",
        materialType: "4",
        element: {},
        cost: 0 
      }],
      microElement: [{
        element:null,
        quantity:null,
        unit:null
      }],
      otherMaterial: [{
        element:"", 
        quantity:"0", 
        unit:"" 
      }],
      costItems: [{ element: "", quantity: "0", unit: "元" }], 
      id: null,
      applyId: null,
      status: null, 
      approveDesc: null,
      formulaName: null,
      selectedFormula: null
    };
    
    // 保存顶部表单的重要字段
    const savedFields = {};
    const keysToPreserve = [
      'customerCode', 'customerName', 'producerId', 'producerName',
      'productName', 'requirement', 'spec', 'amount', 'unit',
      'id', 'applyId', 'status', 'approveDesc', 'remark', 'companyId',
      // 新增以下字段，确保它们在子组件重置时被保留，因为它们由父组件管理
      'formulaName', 'selectedFormula'
    ];
    
    keysToPreserve.forEach(key => {
      if (form.value && form.value[key] !== undefined) {
        savedFields[key] = form.value[key];
      }
    });
    
    // 更新配方字段，保留顶部表单字段
    // 先遍历 form.value，删除不在 keysToPreserve 中的键
    if (form.value) {
      const keysToDelete = [];
      Object.keys(form.value).forEach(key => {
        if (!keysToPreserve.includes(key)) {
          keysToDelete.push(key);
        }
      });
      
      // 分开执行删除操作，避免在迭代时修改对象
      keysToDelete.forEach(key => {
        delete form.value[key];
      });
      
      // 添加配方相关字段
      Object.assign(form.value, configFields);
      
      // 恢复已保存的顶部表单字段.
      Object.assign(form.value, savedFields);
      form.value.id = null;
      form.value.applyId = null;
      form.value.status = null; 
      form.value.approveDesc = null;
    }
    
    // 恢复保存的顶部表单数据
    nextTick(() => {
      restoreTopFormData();
      
      // 最后再次确保materials有默认行
      if (!form.value.materials || !Array.isArray(form.value.materials) || form.value.materials.length === 0) {
        form.value.materials = Array.from({ length: 13 }, (_, index) => ({
          ...defaultTableElements.value,
          num: index + 1
        }));
      }
      // +++ 更新快照 +++
      initialMaterials.value = JSON.parse(JSON.stringify(form.value.materials || []));
      initialPackages.value = JSON.parse(JSON.stringify(form.value.packages || []));
      initialFormSnapshot.value = JSON.stringify(form.value);
    });
  }

// 监听props的变化，如果任何字段变化，则调用handleUpdate函数
watch(
  () => [props.formulaId, props.applyId, props.refresh],
  (newValues, oldValues) => {
    // 安全地获取新值
    const newFormulaId = newValues?.[0];
    const newApplyId = newValues?.[1];
    const newRefresh = newValues?.[2];

    // 安全地获取旧值
    const oldFormulaId = oldValues?.[0];
    const oldRefresh = oldValues?.[2];

    // 如果正在加载中，则忽略此次变更
    if (isLoading.value) {
      return;
    }

    // 检查是否是有效的formulaId（必须是数字且大于0）
    const isValidFormulaId =
      newFormulaId &&
      typeof newFormulaId === "number" &&
      !isNaN(newFormulaId) &&
      newFormulaId > 0;

    // 是否是由关闭对话框触发的变更
    const isDialogCloseTrigger = newFormulaId === null && oldFormulaId !== null;


    // 只在非对话框关闭、有效formulaId且需要更新的情况下加载数据
    if (
      isValidFormulaId &&
      !isDialogCloseTrigger &&
      (!hasInitialized.value ||
        newFormulaId !== oldFormulaId ||
        newRefresh !== oldRefresh)
    ) {
      // 标记为已初始化
      hasInitialized.value = true;

      // 标记为正在加载
      isLoading.value = true;

      // 添加延时以确保弹窗DOM完全挂载
      setTimeout(() => {
        handleUpdate({ id: newFormulaId, refresh: newRefresh }).finally(() => {
          // 无论成功或失败，都标记为加载完成
          isLoading.value = false;
        });
      }, 100);
    } else if (isDialogCloseTrigger) {
      // 重置加载状态
      isLoading.value = false;
      hasInitialized.value = false;
    } else if (!isValidFormulaId && !isDialogCloseTrigger) {
      // 如果是新增表单（无效formulaId但非对话框关闭）
      reset();
    }
  },
  { immediate: true } // 设置为true，立即执行监听器，确保初始值也能触发处理
);

watch(() => quoteForm.value.totalPackageCost, (newVal) => {
  form.value.totalPackageCost = newVal
})

/** 修改按钮操作 */
async function handleUpdate(row, shouldRefresh = true) {
  backupTopFormData();
  isLoading.value = true;
  operateType.value = "update";
  btnName.value = "修改";

  // 保存当前的报价单ID（如果存在）
  const currentApplyId = form.value?.id;

  const formulaIdToLoad = row.id || null;
  if (!formulaIdToLoad) {
    isLoading.value = false;
    return;
  }

  try {
    const mainFormulaResponse = await FormulaApi.getFormula(formulaIdToLoad, shouldRefresh);
    if (!mainFormulaResponse) {
      message.error("获取配方主数据失败或数据为空");
      isLoading.value = false;
      return;
    }
    const formulaData = mainFormulaResponse; 

    const cleanFormulaData = {};
    const formulaKeys = [
      'id', 'name', 'nameRule', 'companyId', 'companyCode', 'companyName',
      'stateCode', 'typeCode', 'npk', 'microElement', 'otherMaterial',
      'otherMaterialQuantity', 'otherMaterialUnit', 'applyId', 'totalAmount',
      'elements', 'totalCost', 'totalRawCost', 'totalOtherCost',
      'totalAuxiliaryCost', 'totalPackageCost', 'totalDynamicCost',
      'processDesc', 'density', 'ph', 'ec', 'waterSoluble', 'appearance',
      'remark', 'status', 'approveNo', 'approveDesc', 'approveTime',
      'approverId', 'approverName', 'rootId', 'sourceId', 'version',
      // 假设 materials 和 packages 现在直接来自 formulaData
      'materials', 'packages' 
    ];
    formulaKeys.forEach(key => {
      if (formulaData.hasOwnProperty(key)) {
        cleanFormulaData[key] = formulaData[key];
      }
    });

    // 保存当前表单中的顶层字段
    const topFormFields = {};
    const fieldsToPreserve = [
      'customerCode', 'customerName', 'producerId', 'producerName',
      'requirement', 'amount', 'unit', 'spec'
    ];
    
    fieldsToPreserve.forEach(field => {
      if (form.value && form.value[field] !== undefined) {
        topFormFields[field] = form.value[field];
      }
    });

    // 更新表单数据
    Object.assign(form.value, cleanFormulaData);
    // 恢复保存的顶层字段（不覆盖用户修改的数据）
    Object.assign(form.value, topFormFields);
    
    // 恢复原始的报价单ID，确保不会被配方ID覆盖
    if (props.operate !== 'addFormula' && props.operate !== 'designFormula') {
      form.value.id = currentApplyId;
    }

    if (!form.value.npk) form.value.npk = { N: null, P: null, PType: "P", K: null, n_unit: '%', k_unit: '%' };
    else {
        form.value.npk.n_unit = form.value.npk.n_unit || '%';
        form.value.npk.k_unit = form.value.npk.k_unit || '%';
        form.value.npk.PType = form.value.npk.PType || 'P';
    }
    if (!form.value.microElement || !Array.isArray(form.value.microElement) || form.value.microElement.length === 0) {
      form.value.microElement = [{ element: null, quantity: null, unit: null }];
    }
    if (!form.value.otherMaterial || !Array.isArray(form.value.otherMaterial) || form.value.otherMaterial.length === 0) {
      form.value.otherMaterial = [{ element: "", quantity: "0", unit: "" }];
    }
    
    // 不再调用restoreTopFormData()，避免覆盖用户修改的数据

    // 处理 materials (假设直接在 formulaData 中)
    if (form.value.materials && Array.isArray(form.value.materials)) {
      form.value.materials.forEach(material => {
        if (material.element && typeof material.element === 'string') {
          try { material.element = JSON.parse(material.element); } catch (e) { material.element = {}; }
        } else if (!material.element) {
          material.element = {};
        }
      });
      initialMaterials.value = JSON.parse(JSON.stringify(form.value.materials));
    } else {
      form.value.materials = [];
      initialMaterials.value = [];
    }

    if (form.value.packages && Array.isArray(form.value.packages)) {
        form.value.packages.forEach(pkg => {
            if (pkg.element && typeof pkg.element === 'string') {
                try { pkg.element = JSON.parse(pkg.element); } catch (e) { pkg.element = {}; }
            } else if (!pkg.element) {
                pkg.element = {};
            }
        });
        initialPackages.value = JSON.parse(JSON.stringify(form.value.packages));
    } 
    else {
      form.value.packages = [];
      initialPackages.value = [];
    }

    if (form.value.materials.length === 0) {
      form.value.materials = Array.from({ length: 13 }, (_, index) => ({
        ...defaultTableElements.value,
        num: index + 1
      }));
    }
    if (form.value.packages.length === 0) {
      form.value.packages.push({ num: 1, materialType: '4', amount: null, price: 0, cost: 0, unit: '元', materialName: "", element: {} });
    }

    calculateCosts();
    calculatePackageCosts();
    calculateTotalCosts();

    // +++ 更新快照 +++
    initialMaterialsForDirtyCheck.value = JSON.parse(JSON.stringify(form.value.materials || [])); // Use separate refs if initialMaterials is for detail comparison
    initialPackagesForDirtyCheck.value = JSON.parse(JSON.stringify(form.value.packages || []));
    initialFormSnapshot.value = JSON.stringify(form.value);
    // Also update initialMaterials and initialPackages if they are used for detail diffing
    initialMaterials.value = JSON.parse(JSON.stringify(form.value.materials || []));
    initialPackages.value = JSON.parse(JSON.stringify(form.value.packages || []));

    isLoading.value = false;

  } catch (error) {
    isLoading.value = false;
  }
}

// 定义emit事件
const emit = defineEmits(["submit-success", "update-success", "close-dialog","company-list"]);

/** 提交按钮 */
function submitForm() {
  // 确保表单数据已更新
  calculateTotalCosts();

  // 移除自动恢复行为 - 避免覆盖用户输入的数据
  // 在修改模式下不应该自动恢复备份的数据

  // 其他提交逻辑
  form.value.applyId = props.applyId? props.applyId : null;
  form.value.operate = props.operate;
  form.value.producerName = props.producerName;
  form.value.producerId = props.producerId;
  // 添加配方， 是否存在重复处理？？？
  if(form.value.operate  === 'addFormula'){
    form.value.id = null;
  }
  // 检查表单引用是否存在
  if (!proxy.$refs.formulaRef) {
    submitFormWithoutValidation();
    return;
  }

  // 执行表单验证
  proxy.$refs.formulaRef.validate((valid) => {
    if (valid) {
      submitFormWithoutValidation();
    } else {
      proxy.$modal.msgError("表单验证失败，请检查输入");
    }
  });
}

// 直接提交表单而不进行验证的辅助函数
async function submitFormWithoutValidation() { 

  if (!form.value) {
    message.error("表单数据不完整，无法提交");
    return;
  }

  const mainFormulaData = {};
  const getNumberOrNull = (val) => {
    if (val === null || val === undefined || val === '') return null;
    const num = Number(val);
    return isNaN(num) ? null : num;
  };
  const getIntegerOrNull = (val) => {
    if (val === null || val === undefined || val === '') return null;
    const num = parseInt(val, 10);
    return isNaN(num) ? null : num;
  };

  const apiTopLevelKeys = [
    'id', 'name', 'nameRule', 'companyId', 'companyCode', 'companyName',
    'stateCode', 'typeCode', 'npk', 'microElement', 'otherMaterial',
    'otherMaterialQuantity', 'otherMaterialUnit', 'applyId', 'totalAmount',
    'elements', 'totalCost', 'totalRawCost', 'totalOtherCost', 
    'totalAuxiliaryCost', 'totalPackageCost', 'totalDynamicCost', 
    'processDesc', 'density', 'ph', 'ec', 'waterSoluble', 'appearance', 
    'remark', 'status', 'approveNo', 'approveDesc', 'approveTime', 
    'approverId', 'approverName', 'rootId', 'sourceId', 'version',
    'quoteFlag', 'operate', 'refreshPrice'
  ];

  apiTopLevelKeys.forEach(key => {
    const formVal = form.value[key];
    let valueToSubmit = undefined;
    switch (key) {
      case 'id':
        valueToSubmit = getIntegerOrNull(formVal);
        if (props.operate === 'addFormula') {
            valueToSubmit = null;
        }
        break;
      case 'companyId': case 'applyId': case 'approverId': case 'rootId': case 'sourceId':
        valueToSubmit = getIntegerOrNull(formVal);
        break;
      case 'status': case 'quoteFlag':
        valueToSubmit = getIntegerOrNull(formVal);
        if (key === 'quoteFlag' && valueToSubmit === null) valueToSubmit = 0;
        break;
      case 'totalAmount': case 'totalRawCost': case 'totalOtherCost': 
      case 'totalAuxiliaryCost': case 'totalPackageCost': case 'density': 
      case 'otherMaterialQuantity':
        valueToSubmit = getNumberOrNull(formVal);
        break;
      case 'totalCost':
        valueToSubmit = getNumberOrNull(formVal);
        if (valueToSubmit === null) valueToSubmit = 0;
        break;
      case 'npk': 
        if (formVal && typeof formVal === 'object' && 
            ((formVal.N !== undefined && formVal.N !== null && formVal.N !== '') || 
            (formVal.P !== undefined && formVal.P !== null && formVal.P !== '') || 
            (formVal.K !== undefined && formVal.K !== null && formVal.K !== '') || 
            (formVal.n_unit) || (formVal.k_unit) || (formVal.p_unit) || (formVal.PType))) { 
          valueToSubmit = { ...formVal, empty: false }; 
        } else {
          valueToSubmit = { empty: true }; 
        }
        break;
      case 'elements': 
      case 'totalDynamicCost':
        if (formVal && typeof formVal === 'object' && Object.keys(formVal).length > 0 && 
            (Object.keys(formVal).length > 1 || !formVal.hasOwnProperty('empty'))) { 
          valueToSubmit = { ...formVal, empty: false };
        } else {
          valueToSubmit = { empty: true };
        }
        break;
      case 'approveTime':
        if (formVal && typeof formVal === 'string' && formVal.trim() !== '') {
          try {
            const d = new Date(formVal);
            if (!isNaN(d.getTime())) valueToSubmit = d.toISOString();
            else valueToSubmit = null;
          } catch { valueToSubmit = null; }
        } else {
          valueToSubmit = null;
        }
        break;
      case 'microElement': 
      case 'otherMaterial': 
        if (Array.isArray(formVal)) {
          valueToSubmit = formVal.filter(
            item => item && item.element && item.quantity !== null && item.quantity !== '' && item.unit
          ).map(item => ({ 
            ...item, 
            quantity: getNumberOrNull(item.quantity) 
          }));
        } else {
          valueToSubmit = [];
        }
        break;
      case 'refreshPrice':
        valueToSubmit = typeof form.value.refreshPrice === 'boolean' ? form.value.refreshPrice : true;
        break;
      case 'operate':
        valueToSubmit = props.operate || "";
        break;
      default:
        if (form.value[key] !== undefined) {
          valueToSubmit = form.value[key];
        } else if (apiTopLevelKeys.includes(key)) {
           valueToSubmit = null; 
        }
        break;
    }
    if (valueToSubmit !== undefined) {
        mainFormulaData[key] = valueToSubmit;
    }
  });

  mainFormulaData.operate = props.operate || mainFormulaData.operate || "";
  if (props.operate === 'addFormula') {
    mainFormulaData.id = null; 
  }

  const isMaterialRowEmpty = (item) => {
    const defaultMat = defaultTableElements.value;
    return !item.materialName && 
           (item.amount === 0 || item.amount === null || item.amount === defaultMat.amount) && 
           (item.price === 0 || item.price === null || item.price === defaultMat.price) &&
           (item.materialType === '' || item.materialType === null || item.materialType === defaultMat.materialType) &&
           (!item.element || Object.keys(item.element).length === 0);
  };

  const isPackageRowEmpty = (item) => {
    return !item.materialName && 
           (item.amount === 1 || item.amount === 0 || item.amount === null) && 
           (item.price === 0 || item.price === null) &&
           item.materialType === '4' && 
           (!item.element || Object.keys(item.element).length === 0);
  };

  let localLoadingSubmitInstance = null;
  try {
    localLoadingSubmitInstance = ElLoading.service({ text: "正在提交主配方数据...", lock: true });
    const isUpdate = mainFormulaData.id != null && props.operate !== 'addFormula';
    let mainFormulaResponse;

    if (isUpdate) {
      mainFormulaResponse = await FormulaApi.updateFormula(mainFormulaData);
    } else {
      mainFormulaResponse = await FormulaApi.createFormula(mainFormulaData);
    }
    if (localLoadingSubmitInstance) localLoadingSubmitInstance.close(); 

    if (mainFormulaResponse) { 
      let formulaIdForDetails;
      let mainApiResponseSuccess = false;

      if (mainFormulaResponse) { 
        formulaIdForDetails = mainFormulaResponse;
        mainApiResponseSuccess = true; 
      } else {
        mainApiResponseSuccess = true;
        formulaIdForDetails = isUpdate ? mainFormulaData.id : mainFormulaResponse; 
        mainApiResponseSuccess = true;
        formulaIdForDetails = mainFormulaData.id;
      }

      if (!mainApiResponseSuccess) {
        message.error(mainFormulaResponse.msg || (isUpdate ? "更新主配方" : "新增主配方") + "失败 (API响应无效)");
        return;
      }
      
      if (!formulaIdForDetails) {
        message.error("获取配方ID失败，无法处理明细。主配方可能已保存，请检查。 ");
        return;
      }
      const detailKeysToCompare = ['num', 'materialId', 'materialCode', 'materialName', 'amount', 'price', 'materialType', 'element', 'priceUnit', 'deduct', 'cost'];
      const detailPromises = [];
      const prepareDetailData = (item, currentFormulaId) => {
        const detailData = { ...item };
        detailData.formulaId = currentFormulaId;
        if (detailData.element && typeof detailData.element === 'object' && Object.keys(detailData.element).length > 0 &&
            (Object.keys(detailData.element).length > 1 || !detailData.element.hasOwnProperty('empty'))) {
          detailData.element = { ...detailData.element, empty: false };
        } else {
          detailData.element = { empty: true };
        }
        detailData.amount = getNumberOrNull(detailData.amount);
        detailData.price = getNumberOrNull(detailData.price);
        detailData.deduct = getNumberOrNull(detailData.deduct);
        detailData.cost = getNumberOrNull(detailData.cost);
        detailData.num = getIntegerOrNull(detailData.num);
        detailData.materialId = getIntegerOrNull(detailData.materialId);
        return detailData;
      };
      // 处理原料及包装明细 
      const currentMaterials = form.value.materials.filter(m => !isMaterialRowEmpty(m));
      if (isUpdate) {
        const initialMaterialIds = new Set(initialMaterials.value.map(m => m.id));
        const currentMaterialIds = new Set();
        currentMaterials.forEach(item => {
          const preparedItem = prepareDetailData(item, formulaIdForDetails);
          if (item.id) { 
            currentMaterialIds.add(item.id);
            const originalItem = initialMaterials.value.find(m => m.id === item.id);
            if (areObjectsDifferent(preparedItem, originalItem, detailKeysToCompare)) {
              detailPromises.push(FormulaDetailApi.updateFormulaDetail(preparedItem));
            }
          } else { 
            detailPromises.push(FormulaDetailApi.createFormulaDetail(preparedItem));
          }
        });
        initialMaterials.value.forEach(initialItem => {
          if (!currentMaterialIds.has(initialItem.id)) {
            detailPromises.push(FormulaDetailApi.deleteFormulaDetail(initialItem.id));
          }
        });
      } else {
        currentMaterials.forEach(item => {
          const preparedItem = prepareDetailData(item, formulaIdForDetails);
          detailPromises.push(FormulaDetailApi.createFormulaDetail(preparedItem));
        });
      }
      const currentPackages = form.value.packages.filter(p => !isPackageRowEmpty(p));
      if (isUpdate) {
        const initialPackageIds = new Set(initialPackages.value.map(p => p.id));
        const currentPackageIds = new Set();
        currentPackages.forEach(item => {
          const preparedItem = prepareDetailData(item, formulaIdForDetails);
          if (item.id) {
            currentPackageIds.add(item.id);
            const originalItem = initialPackages.value.find(p => p.id === item.id);
            if (areObjectsDifferent(preparedItem, originalItem, detailKeysToCompare)) {
              detailPromises.push(FormulaDetailApi.updateFormulaDetail(preparedItem));
            }
          } else {
            detailPromises.push(FormulaDetailApi.createFormulaDetail(preparedItem));
          }
        });
        initialPackages.value.forEach(initialItem => {
          if (!currentPackageIds.has(initialItem.id)) {
            detailPromises.push(FormulaDetailApi.deleteFormulaDetail(initialItem.id));
          }
        });
      } else {
        currentPackages.forEach(item => {
          const preparedItem = prepareDetailData(item, formulaIdForDetails);
          detailPromises.push(FormulaDetailApi.createFormulaDetail(preparedItem));
        });
      }

      if (detailPromises.length > 0) {
        localLoadingSubmitInstance = ElLoading.service({ text: "正在提交配方明细...", lock: true });
        try {
          const results = await Promise.allSettled(detailPromises);
          let allDetailsSuccess = true;
          results.forEach((result, index) => {
            if (result.status === 'rejected' || result.value?.code !== 200) {
              allDetailsSuccess = false;
              }
          });
          if (allDetailsSuccess) {
            message.success("所有配方明细均已成功处理！");
          } else {
            message.warning("配方主体已保存，但部分明细操作存在问题，请核查。建议刷新页面查看最新状态。");
          }
        } catch (settleError) {
          message.error("处理明细时发生未知集中错误。");
        } finally {
          if (localLoadingSubmitInstance) localLoadingSubmitInstance.close();
        }
      } else {
        message.success("配方主体已保存，无明细需要处理。");
      }

      initialMaterials.value = [];
      initialPackages.value = [];

      emit(isUpdate ? "update-success" : "submit-success", mainFormulaResponse.data || mainFormulaResponse);
      open.value = false;
      emit("close-dialog");

    } else {
       message.error(mainFormulaResponse?.msg || (isUpdate ? "更新主配方" : "新增主配方") + "失败 (API响应为空或无效)");
    }
  } catch (error) {
    if (localLoadingSubmitInstance) localLoadingSubmitInstance.close();
    const operationType = (mainFormulaData.id != null && props.operate !== 'addFormula') ? "更新主配方" : "新增主配方";
    message.error(
      operationType +
        "失败: " +
        (error.response?.data?.message || error.message || "网络错误")
    );
  }
}

const materialOptions = ref([]);

// 添加原料分页和加载相关变量
const materialPageNum = ref(1);
const materialPageSize = ref(20);
const materialHasMore = ref(true);
const materialFilterText = ref('');

const queryMaterailParams = ref({
  pageNo: 1,
  type: "1",
  pageSize: 20,
});

// 初始加载原料数据
MaterialApi.getSimpleMaterialPage(queryMaterailParams.value).then((response) => {
  // 转换数据结构，适配PySearch组件所需的格式
  materialOptions.value = response.list.map(item => ({
    label: item.name,
    value: item.name,
    ...item // 保留原始数据以便后续使用
  }));
  
  // 设置是否有更多数据
  materialHasMore.value = response.list.length < response.total;
});

const companys = ref([]);
onMounted(() => {
  // 初始化企业列表数据
  companys.value = [];
  companyPageNum.value = 1;
  companyHasMore.value = true;
  companyLoading.value = false;
  filterText.value = '';
  
  // 初始化原料列表数据
  materialOptions.value = [];
  materialPageNum.value = 1;
  materialHasMore.value = true;
  materialScrollLoading.value = false;
  materialFilterText.value = '';
  
  // 加载初始企业列表
  loadCompanyList(1);
  
});

// 分页加载企业列表数据
const companyPageNum = ref(1);
const companyPageSize = ref(50);
const companyHasMore = ref(true);
const companyLoading = ref(false);

// 加载企业列表数据
const loadCompanyList = (pageNum) => {
  if (companyLoading.value) {
    return;
  }
  
  companyLoading.value = true;
  
  CompanyApi.getCompanyPage({ 
    pageNo: pageNum, 
    pageSize: companyPageSize.value 
  }).then((response) => {
    
    if (pageNum === 1) {
      // 首次加载，直接赋值
      companys.value = response.list;
    } else {
      // 追加数据，注意避免重复
      const newIds = new Set(response.list.map(item => item.id));
      const filteredCurrentCompanys = companys.value.filter(item => !newIds.has(item.id));
      companys.value = [...filteredCurrentCompanys, ...response.list];
    }
    
    // 计算当前已加载的总记录数
    const currentTotal = companys.value.length;
    
    // 判断是否还有更多数据 - 比较当前已加载的总数与API返回的总数
    const hasMore = currentTotal < response.total;
    companyHasMore.value = hasMore;
    
    // 通知父组件
    emit("company-list", companys.value);
  }).catch((error) => {
  }).finally(() => {
    companyLoading.value = false;
  });
};

// 用于存储当前搜索文本
const filterText = ref('');

// 转换企业列表为PySearch组件需要的选项格式
const companysOptions = computed(() => {
  return companys.value.map(item => ({
    label: `${item.shortName || ''} | ${item.name || ''}`,
    value: item.name,
    code: item.code || '',
    shortName: item.shortName || '',
    name: item.name || '',
    // 若关键字段为null，提供默认空字符串避免出错
    pinyin: item.pinyin || '',
    initials: item.initials || '',
    // 保留其他可能需要的属性
    ...item
  }));
});

// 企业选择变更事件
const changeCompany = (value) => {
  // 更新表单中的企业名称
  form.value.companyName = value;

  // 从 companysOptions 查找选中的企业对象以获取 ID 和 Code
  const selectedCompany = companysOptions.value.find(
    (company) => company.value === value // company.value is company.name
  );

  if (selectedCompany) {
    form.value.companyId = selectedCompany.id; // 假设 company对象中有 id 属性
    form.value.companyCode = selectedCompany.code; // 假设 company对象中有 code 属性
  } else {
    form.value.companyId = null;
    form.value.companyCode = null;
  }
  
  // 如果历史配方对话框已打开，则刷新历史配方列表
  if (historyOpen.value && historyRef.value) {
    historyRef.value.queryParams.companyName = value;
    historyRef.value.getList();
  }
};

// 确保displayedElements是一个计算属性
const displayedElements = computed(() => {
  const elements = new Set();
  if (form.value.materials && Array.isArray(form.value.materials)) {
    form.value.materials.forEach((material) => {
      if (material.element && typeof material.element === "object") {
        Object.keys(material.element).forEach((ele) => elements.add(ele));
      }
    });
  }
  return Array.from(elements);
});

const tableData = computed(() => {
  if(!Array.isArray(form.value.materials) || form.value.materials.length === 0){
    return [{ ...defaultTableElements.value }];
  }
  // 过滤掉materialType为'sack'的包装材料，不在原料表格中显示
  const filteredMaterials = form.value.materials.filter(material => material.materialType !== 'sack');
  return filteredMaterials.length > 0 ? filteredMaterials : [{ ...defaultTableElements.value }];
});

const calculateCosts = () => {
  let totalAmount = 0;
  let rawTotalAmount = 0; // 用于计算原材料的总份数（不包括包装袋）

  // 确保materials是一个数组
  if (!form.value.materials || !Array.isArray(form.value.materials)) {
    form.value.materials = [{ ...defaultTableElements.value }];
  }

  // 计算原材料总份数（不包括包装袋）
  form.value.materials.forEach((material) => {
    // TODO  material.materialType !== 'sack' 这个值从哪里来的？
    if (material.materialType !== 'sack') {
      rawTotalAmount += Number(material.amount || 0);
    }
    totalAmount += Number(material.amount || 0);
  });

  // 设置总份数
  form.value.totalAmount = totalAmount;

  // 计算每种材料的成本
  form.value.materials.forEach((material) => {
    // 包装袋材料的成本等于其价格，不受比例影响
    if (material.materialType === 'sack') {
      material.cost = Number(material.price || 0);
    } else {
      // 普通原料按照份数 * 单价计算成本
      const amount = Number(material.amount || 0);
      const price = Number(material.price || 0);
      const cost = (amount * price / (totalAmount == 0?1:totalAmount)).toFixed(4);
      material.cost = unpad(cost);
    }
  });

  // 更新总原料成本
  calculateRawTotalCost();
};

// 计算总原料成本（不包括包装袋）
const calculateRawTotalCost = () => {
  let totalRawCost = 0;

  // 计算原料总成本（不包括包装袋）
  form.value.materials.forEach((material) => {
    if (material.materialType !== 'sack') {
      totalRawCost += Number(material.cost || 0);
    }
  });

  form.value.totalRawCost = totalRawCost.toFixed(decimalPlaces);
};



// 确保safeElements是一个计算属性
const safeElements = computed(() => {
  return form.value.elements || {}; // 如果form.elements是null或undefined，返回一个空对象
});

const addMaterial = () => {
  defaultTableElements.value.num = form.value.materials.length + 1;
  form.value.materials.push({ ...defaultTableElements.value });
  calculateCosts();
};

const removeMaterial = (row) => {
  if (form.value.materials.length > 1) {
    form.value.materials.splice(form.value.materials.indexOf(row), 1);
    calculateCosts();

    // 手动触发元素含量重新计算
    // 由于删除了原料，需要重新计算所有元素的汇总
    nextTick(() => {
      // 模拟表格汇总方法的调用，重新计算元素含量
      const filteredMaterials = form.value.materials.filter(material => material.materialType !== 'sack');
      if (filteredMaterials.length > 0) {
        // 创建模拟的汇总参数
        const mockColumns = [
          { property: 'num' },
          { property: 'materialName' },
          { property: 'amount' },
          ...displayedElements.value.map(element => ({ property: element })),
          { property: 'price' },
          { property: 'cost' },
          { label: '操作' }
        ];

        const mockParams = {
          columns: mockColumns,
          data: filteredMaterials
        };

        // 调用汇总方法重新计算元素含量
        summaryMethod(mockParams);
      } else {
        // 如果没有原料了，清空元素含量
        form.value.elements = {};
      }
    });
  }
};
const toAddCompany = () => {
  openSupplier.value = true
}
watch(
  () => openSupplier.value,
  (newVal) => {
    if (!newVal) {
      openAdd.value = false; // 如果 openAdd 被设置为 false，确保弹窗关闭
        CompanyApi.getCompanyPage({ pageNum: 1, pageSize: 100,name:form.value.companyName }).then((response) => {
          companys.value = response.list;
          emit("company-list", companys.value)
        });
    }else{
      openAdd.value = true
    }
  }
);
const updateMaterial = (row, newName,type) => {
  // 查找选中的材料
  let selectedMaterial = null
  if (type === 'package') {
    selectedMaterial = packagesOptions.value.find(item => item.name === newName)
  }else if(type === 'material'){
    selectedMaterial = materialOptions.value.find(item => item.name === newName)
  }
  if (selectedMaterial) {
    // 处理元素含量数据
    if (selectedMaterial.elements && Array.isArray(selectedMaterial.elements) && selectedMaterial.elements.length > 0) {
      // 验证元素数据的有效性
      const validElements = selectedMaterial.elements.filter(ele =>
        ele &&
        ele.element &&
        (ele.quantity !== null && ele.quantity !== undefined && ele.quantity !== '') &&
        ele.unit
      );

      if (validElements.length > 0) {
        row.element = validElements.reduce((acc, ele) => {
          // 确保数量和单位都存在
          const quantity = ele.quantity;
          const unit = ele.unit || '%'; // 默认单位为%
          acc[ele.element] = quantity + unit;
          return acc;
        }, {});
      } else {
        row.element = {}; // 如果没有有效元素，设置为空对象
      }
    } else {
      row.element = {}; // 如果没有元素，设置为空对象
    }

    row.price = selectedMaterial.purchasePrice || selectedMaterial.averagePurchasePrice || 0;
    row.priceUnit = selectedMaterial.priceUnit|| " ";
    row.materialName = newName; // 确保materialName也被更新
    // 如果当前没有投入份数，设置一个默认值，否则保留原有值
    if (row.amount === null || row.amount === undefined || row.amount === 0) {
      row.amount = 1; // 设置默认投入份数为1，这样用户可以看到元素含量的计算结果
    }
    row.cost = 0; // 重置成本
    row.materialUnit = selectedMaterial.purchaseUnit
    row.materialId = selectedMaterial.id;
    row.materialCode = selectedMaterial.code || "";
    // 计算成本
    calculateCosts();
  }
};
const isMaterialSelected = (materialName) => {
  // 检查材料是否已经被选中（根据材料名称判断）
  return form.value.materials.some((material) => material.materialName === materialName);
};

const isMicroElementSelected = (elementKey) => {
  // 增加检查，确保 form.value.microElement 是一个数组
  return Array.isArray(form.value.microElement) && form.value.microElement.some((ele) => ele && ele.element === elementKey);
};
const packageIsReady = (id) => {
   return form.value.packages.some((packageItem) => packageItem.materialId === id);
}
const materialIsReady = (id) => {
  return form.value.materials.some((material) => material.materialId === id);
}
const isOtherMaterialSelected = (elementKey) => {
  // 增加检查，确保 form.value.otherMaterial 是一个数组
  return Array.isArray(form.value.otherMaterial) && form.value.otherMaterial.some((ele) => ele && ele.element === elementKey);
};
// 设置小数位数，例如保留两位小数
const decimalPlaces = 4;
const summaryMethod = (params) => {
  const { columns, data } = params;
  const summary = [];
  const sums = [];
  summary[0] = "";
  summary[1] = "合计";

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = h("div", { style: { fontWeight:'bold' } }, [
        "合计",
      ]);
      return;
    }
    if(index === 1){
      sums[index] = '';
      return
    }
    if (column.property === "price") {
      sums[index] = "";
      return;
    }
    if(column.property === 'materialName'){
      sums[index] = ''
      return;
    }
    if(column.label === '操作'){
      sums[index] = ''
      return;
    }

    const values = data.map((item) => {
      if (item[column.property]) {
        // 如果不存在，尝试直接使用 item[column.property] 或返回 0
        return Number(item[column.property]) || 0;
      } else if (
        item["element"] &&
        typeof item["element"] === "object" &&
        item["element"][column.property]
      ) {
        // 如果存在，计算 item['element'][column.property] * item['amount']
        // item["element"][column.property] 是带有单位的，需要提取数字部分， 使用正则提取数字部分
        return computeElementContent(item["element"][column.property], item["amount"]);
      } else {
        return 0;
      }
    });

    if (!values.every((value) => Number.isNaN(value))) {
      const total = `${values
        .reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return prev + value; // 这里直接返回累加值
          } else {
            return prev;
          }
        }, 0)
        .toFixed(decimalPlaces)}`; // 使用 toFixed() 格式化结果

      sums[index] = unpad(total);
    } else {
      sums[index] = "";
    }

  });
  statisticTotal(sums);
  return sums;
};

const computeElementContent = (element, amount) => {
  // 处理投入数量为null、undefined或0的情况
  const validAmount = Number(amount) || 0;
  if (validAmount === 0) {
    return 0;
  }

  // 改进正则表达式，支持更多数字格式（包括负数和科学计数法）
  const numberMatch = element.match(/-?\d+(\.\d+)?([eE][+-]?\d+)?/);
  let elementValue = numberMatch ? Number(numberMatch[0]) : 0;

  // 数字后面的为单位
  const unit = element.replace(/-?\d+(\.\d+)?([eE][+-]?\d+)?/, "").trim();

  // 处理不同单位的转换
  if(unit === '%') {
    elementValue = elementValue / 100;
  } else if(unit === '‰' || unit === '‰') {
    elementValue = elementValue / 1000;
  } else if(unit === 'ppm') {
    elementValue = elementValue / 1000000;
  }
  // 如果没有单位或单位不是百分比，假设已经是小数形式

  const result = Number(elementValue * validAmount);

  return result;
}
// 包装表格汇总方法
const summaryPackageMethod = (params) => {
  const { columns, data } = params;
  const sums = [];

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = h("div", { style: { fontWeight: 'bold' } }, ["合计"]);
      return;
    }
    if (index === 1) {
      sums[index] = '';
      return;
    }
    if (column.property === "price") {
      sums[index] = "";
      return;
    }
    if (column.property === 'materialName') {
      sums[index] = '';
      return;
    }
    if (column.label === '操作') {
      sums[index] = '';
      return;
    }

    const values = data.map((item) => {
      if (column.property === 'cost' || column.property === 'amount') {
        return Number(item[column.property]) || 0;
      } else {
        return 0;
      }
    });

    if (!values.every((value) => Number.isNaN(value))) {
      const total = `${values
        .reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return prev + value;
          } else {
            return prev;
          }
        }, 0)
        .toFixed(decimalPlaces)}`;

      sums[index] = unpad(total);
    } else {
      sums[index] = "";
    }
  });

  // 计算包装总成本
  const totalPackageCost = data.reduce((sum, item) => sum + Number(item.cost || 0), 0);
  packageCost.value = Number(totalPackageCost.toFixed(4));
  form.value.totalAuxiliaryCost = packageCost.value;

  // 重新计算总成本
  calculateTotalCosts();

  return sums;
};

// 统计汇总数据
function statisticTotal(sums) {
  // 只统计非包装袋材料的总份数
  const rawMaterials = Array.isArray(form.value.materials) 
    ? form.value.materials.filter(material => material.materialType !== 'sack' && material.materialType !== '4') // 过滤掉包装和辅助材料
    : []; 
  const totalRawAmount = rawMaterials.reduce((sum, material) => sum + Number(material.amount || 0), 0);
  form.value.totalAmount = totalRawAmount; // 更新表单中的总份数（仅原料）
  
  // 包装成本从packageCost获取，表格中已不包含包装材料，确保为数字
  form.value.totalAuxiliaryCost = Number(packageCost.value || 0);
  
  // 确保封装成本字段存在并为数字
  if (form.value.totalPackageCost === undefined || form.value.totalPackageCost === null || form.value.totalPackageCost === '') {
    form.value.totalPackageCost = 0;
  } else {
    form.value.totalPackageCost = Number(form.value.totalPackageCost);
  }
  
  // 计算总成本，只包含原料成本、包装成本和封装成本，并限制精度
  const calculatedTotalCost =
    Number(form.value.totalRawCost || 0) +
    Number(form.value.totalAuxiliaryCost || 0) +
    Number(form.value.totalPackageCost || 0);
    
  form.value.totalCost = Number(calculatedTotalCost.toFixed(4)); // 应用四位小数精度
  
  // 检查 sums 数组是否有足够的元素
  if (sums.length >= displayedElements.value.length + 3) {
    // 重新初始化元素对象，清除所有旧的元素数据
    form.value.elements = {};

    // 先计算并存储每个元素的百分比
    displayedElements.value.forEach((element, index) => {
      // 修正索引计算：从表格结构来看，元素列从第3列开始（序号、原料名称、投入份数、然后是元素列）
      const elementIndex = index + 3;
      const elementSum = Number(sums[elementIndex]) || 0; // 获取该元素的合计值并转换为数字

      // 修正计算公式：元素含量百分比 = (元素总量 / 原料总份数) * 100
      // 不需要乘以defaultRatio.value，因为elementSum已经是实际的元素总量
      // 密度主要影响体积计算，对于重量百分比计算不应该参与
      let value = 0;
      if (totalRawAmount > 0) {
        value = (elementSum / totalRawAmount) * 100; // 转换为百分比
      }

      form.value.elements[element] = unpad(value.toFixed(decimalPlaces));
    });

    // 然后基于计算出的百分比计算 N+P+K 的和
    const nValue = Number(form.value.elements['N'] || 0);
    const pValue = Number(form.value.elements['P'] || 0);
    const kValue = Number(form.value.elements['K'] || 0);
    const npkTotal = nValue + pValue + kValue;

    form.value.elements["N+P+K"] = npkTotal.toFixed(decimalPlaces);
  } else {
    form.value.elements = {};
  }
}

// 函数：去除字符串数字中小数点后不必要的0
function unpad(num) {
  // 先转换为字符串，确保能够应用正则表达式
  const strNum = num.toString();
  // 使用正则表达式去除小数点后不必要的0
  return strNum.replace(/(\.\d*?)0+$/, "$1").replace(/\.$/, "");
}

const changeName = () => {
  form.value.name =
    (form.value.stateCode ? form.value.stateCode : "") + //形态
    (form.value.typeCode ? form.value.typeCode : "") + // 通用类型
    (form.value.companyName ? "-" + form.value.companyName : "-X") + // 企业名称
    (form.value.npk.N ? "-" + form.value.npk.N : "") + //N
    getP_type() + // P
    (form.value.npk.K ? "-" + form.value.npk.K : "") + //K
    getMicroElements() + // 中微量元素
    getOtherMaterialName(); // 其他元素
};

const getOtherMaterialName = () => {
  let r = form.value.otherMaterial
    .filter((element) => element.element && element.quantity && element.unit)
    .map((element) => `${element.element}${element.quantity}`)
    .join("-");
  if (r) {
    return "-" + r;
  } else {
    return "";
  }
};

const getP_type = () => {
  if (form.value.npk.P) {
    if (
      form.value.npk.PType &&
      form.value.npk.PType != "" &&
      form.value.npk.PType != "P"
    ) {
      return "-" + form.value.npk.PType + form.value.npk.P;
    } else {
      return "-" + form.value.npk.P;
    }
  }
  return "";
};

const getMicroElements = () => {
  let r = form.value.microElement
    .filter((element) => element.element && element.quantity && element.unit)
    .map((element) => `${element.element}${element.quantity}`)
    .join("-");
  if (r) {
    return "-" + r;
  } else {
    return "";
  }
};
const addMicroElement = () => {
  form.value.microElement.push({ element: "", quantity: "", unit: "" });
};
const removeMicroElement = (index) => {
  if (form.value.microElement.length > 1) {
    form.value.microElement.splice(index, 1);
  }
};

const addOtherElement = () => {
  // 确保otherMaterial是一个数组
  if (!form.value.otherMaterial || !Array.isArray(form.value.otherMaterial)) {
    form.value.otherMaterial = [];
  }

  form.value.otherMaterial.push({ element: "", quantity: "0", unit: "元" });
  calculateTotalCosts();
};

// 显示添加新原料对话框
const showAddMaterialDialog = () => {
  newMaterialForm.value.name = '';
  newMaterialForm.value.value = '';
  showAddMaterialDialogVisible.value = true;
};

// 处理新增原料的方法
const handleAddNewMaterial = async () => {
  const materialName = newMaterialForm.value.name;
  const materialValue = newMaterialForm.value.value;

  if (!materialName || materialName.trim() === '') {
    message.warning('请输入原料名称');
    return;
  }

  if (!materialValue || materialValue.trim() === '') {
    message.warning('请输入原料代码');
    return;
  }

  // 检查是否已存在该原料（按名称或代码检查）
  const existingMaterial = product_name_abbr.value.find(item =>
    item.label === materialName.trim() || item.value === materialValue.trim()
  );

  if (existingMaterial) {
    message.warning('该原料名称或代码已存在');
    return;
  }

  addMaterialLoading.value = true;

  try {
    // 创建新的字典数据
    const newDictData = {
      dictType: 'product_name_abbr',
      label: materialName.trim(),
      value: materialValue.trim(),
      sort: product_name_abbr.value.length,
      status: 0,
      remark: '用户添加的原料'
    };

    // 调用API创建字典数据
    await createDictData(newDictData);

    // 立即添加到本地数据源中
    product_name_abbr.value.push({
      label: materialName.trim(),
      value: materialValue.trim(),
      colorType: '',
      cssClass: ''
    });

    message.success(`原料 "${materialName}" 添加成功`);
    showAddMaterialDialogVisible.value = false;
    newMaterialForm.value.name = '';
    newMaterialForm.value.value = '';
  } catch (error) {
    message.error('添加原料失败，请重试');
  } finally {
    addMaterialLoading.value = false;
  }
};

const removeOtherElement = (index) => {
  // 确保otherMaterial是一个数组
  if (!form.value.otherMaterial || !Array.isArray(form.value.otherMaterial)) {
    form.value.otherMaterial = [];
    return;
  }

  // 确保索引有效
  if (index >= 0 && index < form.value.otherMaterial.length) {
    form.value.otherMaterial.splice(index, 1);
  }

  calculateTotalCosts();
};

const customizeName = () => {
  isNameInputDisabled.value = !isNameInputDisabled.value;
  customizeButtonName.value =
    customizeButtonName.value === "自定义名称" ? "取消自定义" : "自定义名称";
};

// 计算总成本
const calculateTotalCosts = () => {
  // 包装成本直接使用packageCost变量
  form.value.totalAuxiliaryCost = Number(packageCost.value || 0);

  // 确保封装成本字段存在并为数字
  if (form.value.totalPackageCost === undefined || form.value.totalPackageCost === null || form.value.totalPackageCost === '') {
    form.value.totalPackageCost = 0;
  } else {
    form.value.totalPackageCost = Number(form.value.totalPackageCost);
  }
  
  // 更新总成本，只包含原料成本、包装成本(辅助成本)和封装成本，并限制精度
  const calculatedTotalCost =
    Number(form.value.totalRawCost || 0) +
    Number(form.value.totalAuxiliaryCost || 0) + 
    Number(form.value.totalPackageCost || 0);
    
  form.value.totalCost = Number(calculatedTotalCost.toFixed(4)); // 应用四位小数精度
};
// 更新总成本（封装成本和固定成本）
const updateTotalCosts = (costs) => {
  // 更新封装成本，确保为数字并限制精度
  if (costs.totalPackageCost !== undefined && costs.totalPackageCost !== null) {
    form.value.totalPackageCost = Number(Number(costs.totalPackageCost).toFixed(4));
  } else {
    form.value.totalPackageCost = 0;
  }
  
  // 重新计算总成本
  calculateTotalCosts();
};

// 更新所有相关成本（封装成本和固定成本）
const updateAllCosts = (costs) => {
  // 更新封装成本到总表，确保为数字并限制精度
  if (costs.totalPackageCost !== undefined && costs.totalPackageCost !== null) {
    form.value.totalPackageCost = Number(Number(costs.totalPackageCost).toFixed(4));
  } else {
    form.value.totalPackageCost = 0;
  }
  
  // 重新计算总成本
  calculateTotalCosts();
};

// 添加或更新包装袋材料
const updatePackageCost = (value) => {
  // 将包装成本转换为数字并限制精度
  const cost = Number(Number(value || 0).toFixed(4));
  packageCost.value = cost; // 确保packageCost值更新
  form.value.totalAuxiliaryCost = cost; // 直接更新表单中的辅助成本
  
  // 重新计算总成本
  calculateTotalCosts();
}
// 暴露方法给父组件
defineExpose({
  handleUpdate,
  submitForm,
  reset,
  clear,
  form: form, 
  ApplyApi,
  updateTotalCosts,
  updateAllCosts,
  updatePackageCost,
  hasInitialized,
  getFormData: () => {
    const formData = { ...form.value };

    if (formData.packages && Array.isArray(formData.packages)) {
      formData.packages = formData.packages.filter(pkg => {
        const isDefaultPackage = 
          !pkg.materialName && 
          (pkg.amount === 1 || pkg.amount === 0 || pkg.amount === null) && 
          (pkg.price === 0 || pkg.price === null) &&
          pkg.cost === 0 &&
          pkg.materialType === '4' && 
          (!pkg.element || Object.keys(pkg.element).length === 0 || (Object.keys(pkg.element).length === 1 && pkg.element.empty === true));
        return !isDefaultPackage;
      });
    }
    
    if (formData.materials && Array.isArray(formData.materials)) {
      formData.materials = formData.materials.filter(mat => {
        const isDefaultMaterial = 
          !mat.materialName && 
          (mat.amount === 0 || mat.amount === null) &&
          (mat.price === 0 || mat.price === null) &&
          mat.cost === 0 &&
          (mat.materialType === '' || mat.materialType === null) && 
          (!mat.element || Object.keys(mat.element).length === 0 || (Object.keys(mat.element).length === 1 && mat.element.empty === true));
        return !isDefaultMaterial;
      });
    }
    return formData;
  },
  validate: (callback) => {
    // 动态获取表单引用
    const formulaRef = proxy.$refs.formulaRef;
    if (!formulaRef) {

      const currentTab = activeTab.value; // 假设 activeTab 追踪当前激活的 tab
      if (currentTab === 'basic' && !proxy.$refs.formulaRef) {
        // 如果是基本信息tab，且表单引用不存在，这不正常
        callback(false);
        return;
      }
      if (!formulaRef) {
        callback(false);
        return;
      }
    }
    
    // 调用表单验证
    formulaRef.validate((valid) => {
      callback(valid);
    });
  },
  getFormDirtyStatus: () => { // 添加方法来获取表单状态，替代直接暴露计算属性
    // 手动实现跟 isFormDirty 相同的逻辑，避免依赖计算属性
    if (initialFormSnapshot.value === null) {
      return false;
    }
    const currentSnapshot = JSON.stringify(form.value);
    return currentSnapshot !== initialFormSnapshot.value;
  },
  getFormData: () => { // 添加方法来获取当前表单数据
    return form.value;
  }
});

// 在组件挂载后执行初始化
onMounted(() => {
  // 确保包装数据始终初始化
  ensurePackageData();

  // 检查是否有formulaId，判断是编辑还是新增操作
  if (
    props.formulaId &&
    typeof props.formulaId === "number" &&
    props.formulaId > 0
  ) {
    // 使用延时确保组件DOM已完全渲染
    setTimeout(() => {
      handleUpdate({ id: props.formulaId, refresh: props.refresh });
    }, 100);
  } else {
    // 执行重置操作
    reset(); // reset will now also set the initial snapshot
  }
});

reset();
// 关闭历史配方列表
const closeHistory = () => {
  historyOpen.value = false;
};


// 企业远程搜索方法
const searchCompany = (query) => {
  
  // 重置分页和加载状态
  companyPageNum.value = 1;
  // companyScrollLoading.value = false;
  companyHasMore.value = true;
  
  if (!query) {
    // 当搜索词为空时，加载默认列表
    companys.value = [];
    loadCompanyList(1);
    return;
  }
  
  companyLoading.value = true;
  
  // 保存当前搜索关键词，用于后续加载更多
  filterText.value = query;
  
  // 调用API搜索企业
  CompanyApi.getCompanyPage({ 
    pageNo: 1, 
    pageSize: companyPageSize.value,
    name: query // 使用query作为搜索参数
  }).then((response) => {
    
    // 更新企业列表
    companys.value = response.list;
    
    // 计算是否还有更多数据
    const hasMore = companys.value.length < response.total;
    companyHasMore.value = hasMore;
    
    // 通知父组件
    emit("company-list", companys.value);
  }).catch((error) => {
    message.error('搜索企业失败');
  }).finally(() => {
    companyLoading.value = false;
  });
};
const searchMaterialName = ref('')
const searchMaterial = async (query) => {
  const materialSearchType = activeTab.value === 'package' ? 4 : 1
  searchMaterialName.value = query
  if(activeTab.value === 'package'){
    packagesOptions.value = []
    packagePageNo.value = 1
    packagesScrollLoading.value = false

  }else{
    materialOptions.value = []
    materialPageNum.value = 1
    materialScrollLoading.value = false
  }
  const response = await MaterialApi.getSimpleMaterialPage({
    pageNo:activeTab.value === 'package' ? packagePageNo.value : materialPageNum.value,
    pageSize:100,
    type:materialSearchType,
    name:query,
  })
  if(activeTab.value === 'package' && response.list.length > 0){
    packagesOptions.value = response.list
  }else{
    materialOptions.value = response.list
  }
}


// 防抖定时器 - 原料
let materialScrollTimer = null;

const packagesOptions = ref([])
// 标签页切换事件处理
const handleClick = async (tab) => {
  if (tab.props.name === 'package') {
    // 当切换到包装信息标签页时，确保packages数组已初始化
    const response = await MaterialApi.getMaterialPage({
      pageNo:1,
      pageSize:10,
      type:4
    })
    packagesOptions.value = response.list
  }
};

// 在calculatePackageCosts函数中添加处理
const calculatePackageCosts = () => {
  if (!form.value.packages || !Array.isArray(form.value.packages)) {
    form.value.packages = []; // 如果 packages 不存在或不是数组，则初始化为空数组
  }

  let totalPackageCost = 0;

  // 计算每个包装材料的成本
  form.value.packages.forEach(pkg => {
    // 计算成本 = 数量 * 单价
    const amount = Number(pkg.amount || 0);
    const price = Number(pkg.price || 0);
    pkg.cost = Number((amount * price).toFixed(4)); // 确保 pkg.amount 和 pkg.price 是数字
    totalPackageCost += pkg.cost;
  });

  // 更新包装成本到表单中（限制为4位小数）
  packageCost.value = Number(totalPackageCost.toFixed(4));
  form.value.totalAuxiliaryCost = packageCost.value;

  // 重新计算总成本
  calculateTotalCosts();
};

// 添加包装材料
const addPackage = (pkg) => {
  // 确保packages数组存在
  if (!form.value.packages || !Array.isArray(form.value.packages)) {
    form.value.packages = [];
  }

  // 检查是否存在相同 materialId 的包装材料
  const existingPackage = form.value.packages.some(item => item.materialId === pkg.id);

  if (existingPackage) {
    message.warning('该包装材料已存在')
    return; // 如果存在，则退出函数
  }

  // 创建新的包装材料对象
  const newPackage = {
    num: form.value.packages.length + 1,
    formulaId: props.formulaId || "",
    materialId: pkg.id || null,
    materialCode:pkg.code || "",
    materialName: pkg.name || "",
    amount: null,
    price: pkg.purchasePrice || 0,
    priceUnit: pkg.priceUnit || "",
    materialType: "4", // 包装材料类型为4
    element: {},
    cost: Number((pkg.purchasePrice || 0).toFixed(4)),
  };
  
  // 添加到包装列表
  form.value.packages.push(newPackage);
  // 计算包装成本
  calculatePackageCosts();
};

// 删除包装材料
const removePackage = (row) => {
  if (form.value.packages.length > 1) {
    const index = form.value.packages.indexOf(row);
    if (index !== -1) {
      form.value.packages.splice(index, 1);
      // 重新计算成本
      calculatePackageCosts();
    }
  } else {
    // 如果只有一项，清空其数据但保留在表格中
    if (form.value.packages.length === 1) {
      const singlePackage = form.value.packages[0];
      singlePackage.materialId = null;
      singlePackage.materialCode = "";
      singlePackage.materialName = "";
      singlePackage.amount = 1;
      singlePackage.price = 0;
      singlePackage.cost = 0;
    }
  }
};

// 更新包装材料
const updatePackage = (row, newValue) => {
  // 如果是数量变更，重新计算成本
  calculatePackageCosts();
};

// 定义packageData计算属性，返回包装数据
const packageData = computed(() => {
  // 确保packages数组存在，但不在计算属性中修改
  if (!form.value.packages || !Array.isArray(form.value.packages)) {
    // 初始化在计算属性外完成，这里只返回空数组
    return [];
  }
  return form.value.packages;
});

// 确保包装数组初始化
const ensurePackageData = () => {
  if (!form.value.packages || !Array.isArray(form.value.packages)) { 
    form.value.packages = [{ 
        num: 1, formulaId: form.value.id || "", materialId: null, 
        materialCode: "", materialName: "", amount: null, price: 0, unit: "元", 
        materialType: "4", element: {}, cost: 0 
    }];
  } else if (form.value.packages.length === 0) { 
    form.value.packages.push({ 
        num: 1, formulaId: form.value.id || "", materialId: null, 
        materialCode: "", materialName: "", amount: null, price: 0, unit: "元", 
        materialType: "4", element: {}, cost: 0 
    });
  }
};
</script>
<style scoped lang="scss">
@keyframes loading-rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

:global(.el-select-dropdown__footer) {
  height: 40px !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: hidden !important;
}

:global(.el-select-dropdown__wrap) {
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
}


.addCostTypeBtn {
  display: flex;
  width: 100%;
  height: 40px;
  padding: 0;
  margin: 0;
  font-size: 12px;
  font-weight: normal;
  line-height: 40px;
  color: rgb(4 180 250);
  text-align: center;
  cursor: pointer;
  border-radius: 0;
  box-sizing: border-box;
  transition: all 0.3s;
  align-items: center;
  justify-content: center;
}

.addCostTypeBtn:hover {
  color: #0086e6;
}

.add-material-btn {
  display: flex;
  width: 100%;
  height: 40px;
  padding: 0 12px;
  margin: 0;
  font-size: 12px;
  font-weight: normal;
  line-height: 40px;
  color: #409eff;
  text-align: center;
  cursor: pointer;
  border-radius: 0;
  box-sizing: border-box;
  transition: all 0.3s;
  align-items: center;
  justify-content: center;
  border-top: 1px solid #e4e7ed;
}

.add-material-btn:hover {
  background-color: #f5f7fa;
  color: #337ecc;
}

:global(.el-select-dropdown__footer) {
  padding: 0 !important;
  margin: 0 !important;
}


:global(.el-select-dropdown__wrap) {
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
}

:global(.el-dialog__header) {
  padding: 0 !important;
  margin-right: 0 !important;
}

:global(.el-dialog__footer) {
  padding: 0 10px !important;
}

:global(.el-select-dropdown__item.company-item) {
  display: flex;
  height: 40px !important;
  max-width: 600px;
  padding: 5px 10px !important;
  line-height: 20px !important;
  flex-direction: column;
  justify-content: space-between;
}

span.company-code {
  font-size: 12px;
  color: #999;
}

.company-item:hover .company-name {
  color: #0086e6;
}

.company-item:hover {
  background-color: #f5f5f5;
}

.add-company{
  width: 100%;
  margin: 0;
  line-height: 40px;
  color: rgb(4 180 250);
  // height: 100%;
  text-align: center;
  cursor: pointer;
}

.company-option{
  line-height: 20px;

  p{
    margin: 0;
    font-size:12px;
    line-height: 12px
  }
}

/* 滚动加载提示样式 */
:global(.el-select-dropdown__footer) {
  display: flex !important;
  height: 32px !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: 32px !important;
  color: #909399 !important;
  text-align: center !important;
  border-top: 1px solid #EBEEF5 !important;
  align-items: center !important;
  justify-content: center !important;
}

:global(.el-select-dropdown__footer p) {
  display: flex !important;
  padding: 0 !important;
  margin: 0 !important;
  font-size: 12px !important;
  line-height: 32px !important;
  align-items: center !important;
  justify-content: center !important;
}

:global(.el-select-dropdown__footer .is-loading) {
  margin-right: 5px;
  animation: loading-rotate 1s linear infinite;
}

.loading-more-text {
  display: flex;
  font-size: 12px;
  color: #909399;
  align-items: center;
  justify-content: center;
}

.loading-more-text .el-icon {
  margin-right: 5px;
}

.scroll-hint-text {
  font-size: 12px;
  color: #409EFF;
  cursor: pointer;
}

:deep(.npk-input .el-input-group__prepend) {
  background-color: #fff;
}

.search-more-text{
  width: 100%;
  cursor: pointer;
}

// 其他信息表单样式优化
.other-info-form {
  &.el-form--inline {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 16px;

      .el-form-item__label {
        white-space: nowrap;
        font-weight: 500;
        color: #606266;
      }
    }

    // 小尺寸表单项 (180px)
    .form-item-small {
      .el-input,
      .el-input-number {
        width: 180px;
      }
    }

    // 中等尺寸表单项 (240px)
    .form-item-medium {
      .el-input,
      .el-input-number {
        width: 240px;
      }
    }

    // 大尺寸表单项 (300px)
    .form-item-large {
      .el-input,
      .el-input-number {
        width: 300px;
      }
    }

    // 全宽表单项（仅工艺说明和备注）
    .form-item-full {
      width: 100%;
      margin-right: 0;
      margin-bottom: 20px;

      .el-input,
      .el-textarea {
        width: 100%;
      }
    }

    // 元素含量表单项（行内显示）
    .form-item-elements {
      margin-right: 16px;
      margin-bottom: 16px;

      .el-space {
        .element-input {
          width: 200px;
        }
      }
    }
  }
}
</style>
