import {
  Apply<PERSON>pi
} from "@/api/scm/quote/apply";
import add from "../../rd/formula/add.vue";
import { FormulaApi } from "@/api/scm/rd/formula";
import { MfgCostApi } from "@/api/scm/quote/mfgcost"; // 导入生产成本相关API
import ExcelJS from "exceljs";
import { ref, reactive, watch, nextTick, onMounted, computed, provide } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useForm } from "@/composables/useForm";
import { ElMessage } from "element-plus";
import { useFormulaView } from "./formula-view";
import { useExportHelpers } from "./export-helpers";
import { useSpecHelpers } from "./spec-helpers";
import { CompanyApi } from "@/api/scm/base/company";
import { getStrDictOptions, DICT_TYPE, getDictLabel } from "@/utils/dict";
import { debounce } from "min-dash";

export const f = () => {
  return {
   
  };
};

// 主函数，整合所有模块
export function useApplyFunctions(proxy, emit) {
  // 引入各模块
  const specHelpers = useSpecHelpers(proxy);
  const formulaView = useFormulaView(proxy);
  // 状态数据
  const selectionType = ref("existing"); // 默认选择"从已有配方中选择"
  const selectedFormula = ref("");
  const  formulaOptions= ref([]);
  const loadingFormula = ref(false);
  const applyList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");
  const addComponent = ref(add);
  const addInstance = ref(null);
  const applyId = ref(null);
  const refresh = ref(true);
  const operate = ref("addQuote");
  const formulaId = ref(null);
  const producerName = ref(null); // 生产厂家
  const producerId = ref(null); // 生产厂家ID
  const companyList = ref([])
  // 添加树形选择器所需的变量
  const treeSelectModel = ref(null);
  const customerList = ref([])
  const openSupplier = ref(false)
  const openAdd = ref(true)
  const specTreeData = ref([]);
  const form = ref({});
  const formulaSelect = ref(null)
  const message = useMessage() // 消息弹窗
  const customerLoading = ref(false); // 新增客户加载状态

  // 预览相关状态变量
  const previewOpen = ref(false);
  const previewData = ref({});
  const saving = ref(false);
const addCompanyForm = ref({
  name:form.value.customerName || "",
  isCustomer:1,
  isSupplier:0,
  isProducer:0
})
  // 新增：添加表单数据备份
  const formBackup = ref(null);
  onMounted(async () => {
    const customerResponse = await CompanyApi.getCompanyPage({
      pageNo:customerPageNo,
      pageSize:10,
      isCustomer:1,
    }) 
    if(customerResponse){
      customerList.value = customerResponse.list
    }
  })

  // 新增：恢复表单数据的函数
  const restoreFormData = () => {
    if (formBackup.value && Object.keys(formBackup.value).length > 0) {
      // 只恢复顶部表单字段，避免覆盖配方相关数据
      const keysToRestore = [
        'customerCode', 'customerName', 'customerId' , 'producerId', 'producerName',
        'productName', 'requirement', 'spec', 'amount', 'unit',
        'id', 'approveDesc', 'remark', 'type', 'subType',"status"
      ];
      
      // 确保form对象存在
      if (!form.value) {
        form.value = {};
      }
      
      // 只恢复备份中存在的字段
      keysToRestore.forEach(key => {
        if (formBackup.value[key] !== undefined) {
          form.value[key] = formBackup.value[key];
        }
      });
    }
  };

  // 在打开对话框时备份数据
  watch(open, (newVal) => {
    if (newVal === true) {
      getCompanyList()
      // 只有在新增模式下才启用自动恢复功能
      if (!form.value.id) { 
        const checkInterval = setInterval(() => {
          // 如果表单中的关键字段为空但备份中有数据，则恢复
          if (form.value && (!form.value.customerName || !form.value.requirement) && formBackup.value) {
            restoreFormData();
          }
        }, 300); // 每300ms检查一次
        
        // 在5秒后清除定时器
        setTimeout(() => clearInterval(checkInterval), 5000);
      }
    } else { // 当 newVal === false (对话框关闭时)
      // 不替换整个 form 对象，而是调用 reset 函数来清理
      reset(); 
    }
  });

  // 配方选择相关状态
  const formulaPageNo = ref(1); // 配方列表当前页码
  const formulaPageSize = ref(10); // 配方列表每页大小
  const formulaTotal = ref(0); // 配方总数
  const loadingMoreFormula = ref(false); // 是否正在加载更多配方
  const lastFormulaQuery = ref(''); // 保存上次搜索关键词

  // 配方查看相关状态
  const formulaViewOpen = ref(false);
  const formulaLoading = ref(false);
  const activeFormulaTab = ref('basic');
  const formulaDetail = ref({});
  const formulaMaterials = ref([]); // 配方原料明细
  const formulaPackagingMaterials = ref([]); // 配方包材明细

  // 数据字典
  const quote_status = getStrDictOptions(DICT_TYPE.QUOTE_STATUS);
  const measure_unit = getStrDictOptions(DICT_TYPE.MEASURE_UNIT);
  const product_state = getStrDictOptions(DICT_TYPE.PRODUCT_STATE);
  const product_category = getStrDictOptions(DICT_TYPE.PRODUCT_CATEGORY);

  provide('quoteForm', form); // 提供名为 'quoteForm' 的 ref

  const data = reactive({
    queryParams: {
      pageNo: 1,
      pageSize: 10,
      customerId: null,
      customerCode: null,
      customerName: null,
      productName: null,
      requirement: null,
      amount: null,
      unit: null,
      deliveryDate: null,
      status: null,
      quote_flag: 0,
    },
    rules: {
      createBy: [
        { required: true, message: "创建者ID不能为空", trigger: "blur" },
      ],
      createTime: [
        { required: true, message: "创建时间不能为空", trigger: "blur" },
      ],
      amount: [
        { 
          validator: (rule, value, callback) => {
            if (value === undefined || value === null || value === '') {
              callback(); // 空值允许通过，如果需要必填可以添加required规则
              return;
            }
            // 验证是否为整数
            const intValue = parseInt(value);
            if (isNaN(intValue) || intValue.toString() !== value.toString()) {
              callback(new Error('加工数量必须是整数'));
            } else {
              callback();
            }
          }, 
          trigger: ['blur', 'change'] 
        }
      ],
    },
  });

  const { queryParams, rules } = data;

  const getCompanyList = (value,query) => {
    try{
      CompanyApi.getCompanyPage({
        isProducer:1,
        name:query || ''
      }).then(res => {
        companyList.value = res.list
      })
    }catch(error){

    }
  }
  const customerOptions = computed(() => {
    return customerList.value.map(item => ({
      label: `${item.shortName || ''} | ${item.name || ''}`,
      value: item.name, // 使用名称作为直接值
      code: item.code || '',
      shortName: item.shortName || '',
      name: item.name || '',
      customerCode: item.customerCode || item.code || '',
      customerId: item.id || '',
      ...item
    }))
  })
  const toAddCompany = () => {
    openSupplier.value = true
  }
  watch(
    () => openSupplier.value,
    (newVal) => {
      if (!newVal) {
        openAdd.value = false; // 如果 openAdd 被设置为 false，确保弹窗关闭
        CompanyApi.getCompanyPage({
          pageNo: 1,
          pageSize: 100,
          name: form.value.companyName,
        }).then((response) => {
          companyList.value = response.list;
          if (emit) {
            emit("company-list", companyList.value);
          }
        });
      } else {
        openAdd.value = true;
      }
    }
  );
  const changeCustomer = (value) => {
    
    if (!value) {
      // 处理清空选择的情况
      form.value.customerName = '';
      form.value.customerCode = '';
      form.value.customerId = '';
      return;
    }
    
    if (typeof value === 'object') {
      // 处理对象形式（PySearch会传递完整对象）
      form.value.customerName = value.name || '';
      form.value.customerCode = value.customerCode || value.code || '';
      form.value.customerId = value.id || '';
    } else if (typeof value === 'string') {
      // 处理字符串形式
      const matchedCustomer = customerList.value.find(item => 
        item.name === value || 
        item.shortName === value || 
        `${item.shortName || ''} | ${item.name || ''}` === value
      );
      
      if (matchedCustomer) {
        form.value.customerName = matchedCustomer.name || '';
        form.value.customerCode = matchedCustomer.customerCode || matchedCustomer.code || '';
        form.value.customerId = matchedCustomer.id || null;
        const customerShortName = customerOptions.value.filter(item => item.customerCode === form.value.customerCode)[0].shortName
        FormulaApi.getFormulaPage({
          name: customerShortName,
        }).then(response => {
          if(response.list.length > 0){
            formulaOptions.value = response.list;

          } else {
            message.error('该客户暂无配方信息')
          }
        })
      } else {
        // 只能设置名称，无法确定编码
        form.value.customerName = value;
        form.value.customerCode = '';
      }
    }
    // 备份表单数据
  }

  const customerPageNo = ref(1)
  const scrollLoad = debounce(async (type) => {
    if(type === 'formula'){
      formulaPageNo.value++
      const response = await FormulaApi.getFormulaPage({
        pageNo:formulaPageNo.value,
        pageSize:10,
        status:3,
      })
      formulaOptions.value.push(...response.list)
    }else if(type === 'customer'){
      customerPageNo.value++
      const response = await CompanyApi.getCompanyPage({
        pageNo:customerPageNo.value,
        pageSize:10,
        isCustomer:1,
      })
      customerList.value.push(...response.list)
    }
  })


  // 创建导出助手
  const exportHelpers = useExportHelpers(proxy, specHelpers.specTreeData);
  /** 查询报价申请列表 */
  function getList() {
    loading.value = true;

    // 添加完整配方数据请求标志
    const queryParamsWithOptions = {
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize,
      customerCode: queryParams.customerCode,
      customerName: queryParams.customerName,
      customerId: queryParams.customerId,
      productName: queryParams.productName,
      requirement: queryParams.requirement,
      amount: queryParams.amount,
      unit: queryParams.unit,
      deliveryDate: queryParams.deliveryDate,
      status: queryParams.status,
      quote_flag: queryParams.quote_flag,
      includeFormula: true, // 请求包含完整配方数据
    };
    ApplyApi.getApplyPage(queryParamsWithOptions)
      .then((response) => {
        applyList.value = response.list;
        total.value = response.total;
        loading.value = false;
      })
      .catch((error) => {
        loading.value = false;
      });
  }

  const remoteFormula = (query, loadMore = false) => {
    if (typeof query !== 'string') {
      query = ''; // Ensure query is a string
    }
    
    if (!loadMore) {
      // 如果不是加载更多，是新的搜索或首次加载
      formulaPageNo.value = 1; // 重置页码
      formulaOptions.value = []; // 清空现有选项
      formulaTotal.value = 0; // 重置总数
      lastFormulaQuery.value = query; // 保存当前搜索词
      loadingFormula.value = true; // 设置为初始加载状态
    } else {
      // 如果是加载更多
      if (loadingMoreFormula.value || formulaOptions.value.length >= formulaTotal.value) {
        // 如果正在加载或者已经加载完所有数据，则不执行
      return;
      }
      loadingMoreFormula.value = true; // 设置为加载更多状态
      // 使用上次的搜索词
      query = lastFormulaQuery.value; 
    }
    
    const params = {
      pageNo: formulaPageNo.value,
      pageSize: formulaPageSize.value,
      name: query || undefined,
      status: 3, // 只加载审核通过的
    };

    FormulaApi.getFormulaPage(params)
      .then((response) => {
        if (response && response.list) {
          if (loadMore) {
            // 加载更多：使用 push 追加数据
            formulaOptions.value.push(...response.list); 
          } else {
            // 初始加载/搜索：替换数据
            formulaOptions.value = response.list;
          }
          formulaTotal.value = response.total; // 更新总数
        }
      })
      .catch((error) => {

        message.error("获取配方列表失败");
      })
      .finally(() => {
        loadingFormula.value = false;
        loadingMoreFormula.value = false;
      });
  };

  // 表单重置
  function reset() {
    // 创建一个干净的表单对象模板
    const cleanForm = {
      id: null,
      customerCode: null,
      customerName: null,
      customerId: null,
      productName: null,
      requirement: null,
      amount: 1,
      unit: "t", // 设置默认单位
      deliveryDate: null,
      status: null,
      remark: null,
      // 以下字段可以省略，不会影响表单提交
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null,
      delFlag: null,
      // 初始化空数组，而不是在其他地方追加
      npk: [
        { element: "N", quantity: "0.0", unit: "%" },
        { element: "P", quantity: "0.0", unit: "%" },
        { element: "K", quantity: "0.0", unit: "%" },
      ],
      microElement: [{ element: "", quantity: "0.0", unit: "" }],
      otherMaterial: [{ element: "", quantity: "0.0", unit: "" }],
      // 重要字段设为null
      formulaId: null,
      formulaName: null, // 新增：用于显示配方名称
      quoteFormulaId: null,
      quoteFormula: null,
      // 规格相关字段
      type: null,
      spec: null,
    };

    // 用Object.assign更新form对象，而不是重新赋值
    Object.keys(form.value).forEach(key => {
      delete form.value[key];
    });
    Object.assign(form.value, cleanForm);

    // 重置树形选择模型
    treeSelectModel.value = null;

    // 确保外部formulaId变量也被重置
    formulaId.value = null;
    
    // 清空配方选项，避免已选配方的干扰
    formulaOptions.value = [];
    
    // 清空备份数据
    formBackup.value = null;
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.pageNo = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    // 清空ID，确保不会加载任何旧数据
    formulaId.value = null;
    applyId.value = null;

    // 彻底重置表单状态
    reset();

    // 设置标题和默认值
    title.value = "添加报价单";
    form.value.npk = [
      { element: "N", quantity: "", unit: "%" },
      { element: "P", quantity: "", unit: "%" },
      { element: "K", quantity: "", unit: "%" },
    ];
    form.value.microElement = [{ element: "", quantity: "0.0", unit: "" }];
    form.value.otherMaterial = [{ element: "", quantity: "0.0", unit: "" }];
    form.value.unit = "t";

    // 先打开对话框
    open.value = true;

    // 使用nextTick确保DOM更新后再执行后续操作
    nextTick(() => {
      // 如果子组件存在，则重置其状态
      if (addInstance.value) {
        setTimeout(() => {
          if (typeof addInstance.value.clear === "function") {
            addInstance.value.clear();
          } else if (typeof addInstance.value.reset === "function") {
            addInstance.value.reset();
          }
        }, 50);
      }
    });
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    // 重置表单
    reset();

    const _id = row.id || ids.value; // Ensure _id is correctly determined

    title.value = "修改报价单";
    operate.value = 2;

    loading.value = true; 

    ApplyApi.getApply(_id)
      .then((response) => {
        loading.value = false;
        
        // Check if the response itself is valid (e.g., has an ID)
        if (response && response.id) { 
          // 先确保现有的 form.value 对象存在
          if (!form.value) {
            form.value = {};
          }
          
          // 清除现有键但保留对象引用
          Object.keys(form.value).forEach(key => {
            delete form.value[key];
          });
          
          // 先使用展开运算符创建响应对象的深拷贝，避免直接引用
          const responseData = { ...response };
          
          // 将响应数据分配到表单 - 使用 Object.assign 维持响应式
          Object.assign(form.value, responseData);
          
          
          if (response.quoteFormulaId && response.status >= 4) {
            formulaId.value = response.quoteFormulaId;
          } else if (response.formulaId && response.status < 4) {
            formulaId.value = response.formulaId;
          }
          treeSelectModel.value = getDictLabel('prod_spec',response.spec)
          form.value.formulaName = response.productName
          // 预先清空options，避免数据不一致
          formulaOptions.value = [];
          
          if (formulaId.value) {
            FormulaApi.getFormula(formulaId.value, true).then(formulaResponse => {
              if (formulaResponse && formulaResponse.data) {
                const formulaData = formulaResponse.data;
                
                // 确保options初始化为数组
                formulaOptions.value = formulaOptions.value || [];
                
                // 添加当前配方到选项列表，确保下拉菜单有数据
                const exists = formulaOptions.value.some(item => item.id === formulaData.id);
                if (!exists) {
                  formulaOptions.value.push(formulaData);
                }
                
                // 确保关联配方信息正确设置
                if (form.value) {
                  form.value.formulaName = formulaData.name;
                  form.value.selectedFormula = formulaData;
                  form.value.formulaId = formulaData.id;
                }
                
              }
            }).catch(error => {
              // 如果无法获取配方详情，尝试至少获取配方列表
              remoteFormula("", false);
            });
          } else {
            // 如果没有配方ID，至少加载空列表
            remoteFormula("", false);
          }

          if (form.value.type && form.value.spec) {
            if (form.value.type === "powder" && form.value.subType !== undefined) {
              treeSelectModel.value = `${form.value.spec}__${form.value.subType}`;
            } else {
              treeSelectModel.value = form.value.spec;
            }
          } else if (form.value.type) {
            treeSelectModel.value = form.value.type;
          }

          // 使用 setTimeout 创建一个定时器，在对话框打开后多次检查表单数据
          const scheduleDataChecks = () => {
            let checks = 0;
            const maxChecks = 5; 
            
            const checkInterval = setInterval(() => {
              checks++;
              
              // 检查表单数据是否存在
              const hasData = form.value && form.value.customerName && form.value.requirement;
              
              if (!hasData && formBackup.value) {
                restoreFormData();
              } else if (hasData) {
              }
              
              // 达到最大检查次数后停止
              if (checks >= maxChecks) {
                clearInterval(checkInterval);
              }
            }, 800);  // 每800ms检查一次
          };

          // 确保对话框打开前表单数据已加载完毕
          open.value = false; 
          nextTick(() => {
            applyId.value = response.id; 
            
            // 使用延迟确保后续操作在下一个事件循环执行
            setTimeout(() => {
              open.value = true;
              
              // 开始定期检查表单数据
              scheduleDataChecks();
              
              // 再次检查并确保表单数据存在
              if (!form.value || !Object.keys(form.value).length) {
                Object.assign(form.value, responseData);
              }

              nextTick(() => {
                if (
                  addInstance.value &&
                  typeof addInstance.value.handleUpdate === "function"
                ) {
                  // 给子组件足够的时间初始化
                  setTimeout(() => {
                    addInstance.value.handleUpdate({
                      id: formulaId.value, 
                      refresh: true,
                      fromParent: true,
                    });
                    // 再次检查并确保父组件表单数据在子组件加载后仍然存在
                    if (form.value && (!form.value.customerName || !form.value.requirement)) {
                      Object.assign(form.value, responseData);
                    }
                  }, 300);
                } else if (addInstance.value) {
                  if (typeof addInstance.value.loadFormula === "function") {
                    addInstance.value.loadFormula(formulaId.value);
                  } else if (typeof addInstance.value.init === "function") {
                    addInstance.value.init(formulaId.value);
                  } else {
                     message.warning("加载配方数据失败，请重新选择配方");
                  }
                }
              });
            }, 50);
          });
        } else {
          message.error("获取报价单数据失败或数据无效"); 
        }
      })
      .catch((error) => {
        loading.value = false;
        message.error("获取报价单数据时出错");
      });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["applyRef"].validate((valid) => {
      if (valid) {
        // 1. 获取父组件的报价申请数据 (Apply Data)
        const applyDataFromForm = JSON.parse(JSON.stringify(form.value)); 

        // 2. 获取子组件的配方数据 (Formula Data)
        let formulaDataFromChild = null;
        if (addInstance.value) {
           try {
             if (typeof addInstance.value.getFormData === 'function') { formulaDataFromChild = addInstance.value.getFormData(); }
             else if (addInstance.value.form) { formulaDataFromChild = addInstance.value.form; }
             if (formulaDataFromChild && !formulaDataFromChild.id && formulaId.value) { formulaDataFromChild.id = formulaId.value; }
             if (!formulaDataFromChild || typeof formulaDataFromChild !== 'object') {
                formulaDataFromChild = { id: formulaId.value }; 
             }
           } catch (error) { /* ... */ return; }
        } else {
          if (!formulaId.value) { message.error("缺少关联的配方信息"); return; }
          formulaDataFromChild = { id: formulaId.value }; 
        }

        // 补充可能缺失的顶层字段
        if (!applyDataFromForm.microElement && formulaDataFromChild.microElement) {
          applyDataFromForm.microElement = formulaDataFromChild.microElement;
        }
        
        if (!applyDataFromForm.otherMaterial && formulaDataFromChild.otherMaterial) {
          applyDataFromForm.otherMaterial = formulaDataFromChild.otherMaterial;
        }
        
        if (!applyDataFromForm.npk && formulaDataFromChild.npk) {
          applyDataFromForm.npk = formulaDataFromChild.npk;
        }

        // 3. 精确构造发送给 API 的 payload (ApplySaveReqVO)
        const payload = {
            // --- 只包含 ApplySaveReqVO 明确定义的顶层字段 ---
            id: applyDataFromForm.id || null, // 使用 applyDataFromForm.id，如果不存在则为null (用于创建)
            customerCode: applyDataFromForm.customerCode,
            customerName: applyDataFromForm.customerName,
            customerId: applyDataFromForm.customerId,
            producerId: applyDataFromForm.producerId,
            producerName: applyDataFromForm.producerName,
            productName: applyDataFromForm.productName,
            requirement: applyDataFromForm.requirement,
            spec: applyDataFromForm.spec,
            amount: applyDataFromForm.amount ? parseInt(applyDataFromForm.amount) : 0,
            unit: applyDataFromForm.unit,
            deliveryDate: null,
            status: applyDataFromForm.status,
            approveInfo: applyDataFromForm.approveDesc,
            remark: applyDataFromForm.remark,
            applyerId: applyDataFromForm.applyerId,
            applyerName: applyDataFromForm.applyerName,
            npk: applyDataFromForm.npk,
            microElements: applyDataFromForm.microElement,
            otherMaterials: applyDataFromForm.otherMaterial,
            formulaId: applyDataFromForm.formulaId,
            version: applyDataFromForm.version,
            quoteFormulaId: null,
            totalCost: 0,
            totalRawCost: 0,
            totalOtherCost: 0,
            totalAuxiliaryCost: 0,
            totalPackageCost: 0,
            totalDynamicCost: null,
            companyId: applyDataFromForm.companyId,
            appearance: applyDataFromForm.appearance,
            quoteFormula:null
        };

        // 4. 处理 deliveryDate
        if (applyDataFromForm.deliveryDate) {
            try {
                payload.deliveryDate = new Date(applyDataFromForm.deliveryDate).toISOString();
            } catch (e) { payload.deliveryDate = null; }
        }

        // 5. 处理嵌套的 quoteFormula (清理和修正类型)
        if (formulaDataFromChild && typeof formulaDataFromChild === 'object' && formulaDataFromChild.id) {
            const cleanFormulaData = { ...formulaDataFromChild }; // 使用 cleanFormulaData 进行操作

            // 将配方中的成本同步到顶层 payload (仅同步ApplyVO中定义的字段)
            ['totalCost', 'totalRawCost', 'totalOtherCost', 'totalAuxiliaryCost', 'totalPackageCost'].forEach(field => {
                if (cleanFormulaData[field] !== undefined && cleanFormulaData[field] !== null) {
                    payload[field] = Number(cleanFormulaData[field]);
                }
            });
            // totalDynamicCost 也应处理
            if (cleanFormulaData.totalDynamicCost !== undefined && cleanFormulaData.totalDynamicCost !== null && String(cleanFormulaData.totalDynamicCost).toLowerCase() !== 'null') {
                payload.totalDynamicCost = String(cleanFormulaData.totalDynamicCost);
            } else {
                payload.totalDynamicCost = null;
            }
        } else {
             payload.quoteFormulaId = formulaId.value; 
        }
        // 6. 确保顶层成本字段最终是数值 (可能已被配方数据覆盖)
        ['totalCost', 'totalRawCost', 'totalOtherCost', 'totalAuxiliaryCost', 'totalPackageCost'].forEach(field => {
          payload[field] = Number(payload[field] || 0); 
        });
        if (payload.totalDynamicCost !== undefined && payload.totalDynamicCost !== null && String(payload.totalDynamicCost).toLowerCase() !== 'null') {
            payload.totalDynamicCost = String(payload.totalDynamicCost);
        } else {
            payload.totalDynamicCost = null; // 确保传递真正的 null
        }
        if(formulaId.value !== null) {
          payload.quoteFormula = addInstance.value.getFormData()
          payload.quoteFormula.id = formulaId.value
          payload.quoteFormula.totalPackageCost = Number(form.value.totalPackageCost)
        }
        payload.producerId = form.value.producerId
        payload.producerName = producerName.value
        payload.customerCode = form.value.customerCode
        payload.customerName = form.value.customerName
        payload.customerId = form.value.customerId
        payload.spec = form.value.spec
        payload.amount = Number(form.value.amount)
        payload.totalPackageCost = Number(form.value.totalPackageCost)

        
        // 7. 调用 API
        if (payload.id != null) {
          if(payload.status < 4){
            payload.quoteFormulaId = null
            payload.formulaId = formulaId.value
          }else{
            payload.formulaId = null
            payload.quoteFormulaId = formulaId.value
          }

          ApplyApi.updateApply(payload).then(async (response) => {
            try { 
              if (response) {
                message.success("修改成功");
                open.value = false;
                getList();
              } else { 
                message.error("修改报价单失败");
              }
            } catch (outerError) {
              message.error("处理更新结果时发生内部错误");
            }
          }).catch(updateApplyError => {
             message.error("修改报价单失败 (API 调用出错)" );
          });
        } else {
          payload.formulaId = null;
          payload.quoteFormulaId = null;
          ApplyApi.createApply(payload).then(async (createApplyResponse) => {
            if (createApplyResponse === null) {
              message.success("新增成功"); 
              getList();
              open.value = false
            } else {
              message.error("新增报价单失败");
            }
          }).catch(error => {
            message.error("新增报价单失败");
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    message
      .confirm('是否确认删除报价单编号为"' + _ids + '"的数据项？')
      .then(function () {
          return ApplyApi.deleteApply(_ids); // 假设 delApply 返回 Promise
      })
      .then(() => {
        getList();
        message.success("删除成功");
      })
      .catch(() => {});
  }

  const changeFormula = (value) => {
    // 如果value是对象，说明是选择了一个配方对象
    if (value && typeof value === 'object') {
      // 保存当前表单的ID，避免被覆盖
      const currentFormId = form.value.id;
      
      // 更新配方相关字段
      formulaId.value = value.id;
      form.value.productName = value.name || "";
      
      // 保存选中的配方对象到表单中，防止被清空
      form.value.selectedFormula = value;
      // 确保formulaName也被正确设置为配方名称字符串
      form.value.formulaName = value.name;
      
      // 恢复表单ID，确保不会被配方ID覆盖
      form.value.id = currentFormId;
      
      loadingFormula.value = true; 
      // message.notify("正在加载配方数据...");
      
      FormulaApi.getFormula(value.id, true)
        .then(async (response) => {
          loadingFormula.value = false;
          // 改进判断：确保 response.data 不仅存在，而且包含有效内容（例如检查是否有 id）
          if (response) { 
            // 再次保存当前表单ID，避免在加载配方数据时被覆盖
            const savedFormId = form.value.id;
            
            const formulaData = response;
            form.value.productName = formulaData.name || "";
            // 确保表单中的配方名称正确设置
            form.value.formulaName = formulaData.name;
            
            // 确保表单ID不变
            form.value.id = savedFormId;
            nextTick(() => {
              const childComponent = addInstance.value;
              if (childComponent && typeof childComponent.handleUpdate === 'function') {
                // 保存最后一次的表单ID
                const finalSavedFormId = form.value.id;
                
                // 将获取到的 formulaData 传递给子组件
                childComponent.handleUpdate(formulaData, true); // 假设 handleUpdate 第一个参数是数据，第二个是 refresh 标志
                // 在子组件更新后再次确保表单ID不变
                nextTick(() => {
                  form.value.id = finalSavedFormId;
                });
              } else {
                message.warning("子组件无法加载配方数据"); 
              }
            });
          } else {
             // 处理数据为空或无效的情况
             // 可能需要清空子组件状态
             nextTick(() => {
               const childComponent = addInstance.value;
               if (childComponent) {
                 if (typeof childComponent.clear === "function") {
                   childComponent.clear();
                 } else if (typeof childComponent.reset === "function") {
                   childComponent.reset();
                 }
               }
             });
          }
        })
        .catch((error) => {
          loadingFormula.value = false;
        });
    } else {
      // 清空选择的逻辑
      formulaId.value = null;
      form.value.formulaId = null;
      form.value.quoteFormulaId = null;
      form.value.productName = null; // 清空产品名称
      form.value.formulaName = null; // 清空配方名称
      form.value.selectedFormula = null; // 清空选中的配方对象
      
      nextTick(() => {
        const childComponent = addInstance.value;
        if (childComponent) {
          if (typeof childComponent.clear === "function") {
            childComponent.clear();
          } else if (typeof childComponent.reset === "function") {
            childComponent.reset();
          }
        }
      });
    }
  };

  // 取消按钮
  function cancel() {
    open.value = false;
    // 先清空ID，确保不会加载任何旧数据
    formulaId.value = null;
    applyId.value = null;
    form.value = {

    }
    // 重置表单状态
    reset();

    // 使用nextTick确保DOM更新后再执行后续操作
    nextTick(() => {
      // 如果子组件存在，则清理其状态
      if (addInstance.value) {
        if (typeof addInstance.value.clear === "function") {
          addInstance.value.clear();
        } else if (typeof addInstance.value.reset === "function") {
          addInstance.value.reset();
        }
      }

      // 标记刷新以确保下次加载数据时获取新数据
      refresh.value = !refresh.value;
    });
  }

  // 确认报价
  function confirmQuote(row) {
    message
      .confirm("确认接受该报价吗？")
      .then(function () {
        // 使用 loading ref 控制加载状态
        loading.value = true;
        message.notify("正在提交...");
        const query = {
          id:Number(row.id)
        }
        ApplyApi.confirmApply(query).then(() => {
          loading.value = false;
          message.success("确认报价成功");
          getList()
        }).catch((error) => {
          loading.value = false;
          message.error("确认报价失败");
        })
      })
      .catch(() => {});
  }

  // 设置字典数据并初始化规格树
  function setSpecDictData(prodSpecData, prodSpecSubTypeData, materialStateData) {
    return new Promise((resolve, reject) => {
      try {
        if (prodSpecData && prodSpecSubTypeData && materialStateData) {
          specHelpers.setDictionaryData(
            prodSpecData, 
            prodSpecSubTypeData, 
            materialStateData
          );
          resolve(true);
        } else {
          // 尝试使用标准方法加载字典
          specHelpers.ensureDictLoaded()
            .then(() => resolve(true))
            .catch(err => {
              reject(err);
            });
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  // 自定义onMounted添加到导出函数中
  function initialize() {
    
    // 确保规格数据已加载
    try {
      // 首先尝试直接调用确保字典加载的方法
      if (typeof specHelpers.ensureDictLoaded === 'function') {
        specHelpers.ensureDictLoaded().then(() => {
          // 同步到当前模块的specTreeData
          specTreeData.value = specHelpers.specTreeData.value;
        }).catch(err => {
          message.error("加载字典数据失败"); 
        });
      } else {
        // 如果没有该方法，回退到调用初始化规格树;
        specHelpers.initSpecTreeData();
        // 同步数据
        specTreeData.value = specHelpers.specTreeData.value;
      }
    } catch (error) {
    }
    
    // 获取列表数据
    getList();
    
  }
  
  // 调用初始化
  initialize();

  // 新增：加载更多配方的函数
  const loadMoreFormula = () => {
    // 检查是否可以加载更多
    if (formulaOptions.value.length < formulaTotal.value && !loadingMoreFormula.value) {
      formulaPageNo.value++; // 页码增加
      remoteFormula(lastFormulaQuery.value, true); // 调用 remoteFormula 加载更多
    }
  };

  // 新增 searchCustomer 函数
  const searchCustomer = (query) => {
    if (customerLoading.value) {
      return;
    }
    customerLoading.value = true;
    CompanyApi.getCompanyPage({
      pageNo: 1, // 或者根据需要实现分页
      pageSize: 50, // 或者根据需要设置页面大小
      name: query || '',
      isCustomer: 1, // 假设API支持通过 isCustomer: 1 来筛选客户
      // 如果API不支持 isCustomer, 则需要获取所有公司再在前端筛选，或者修改API
    }).then(response => {
      if (response && response.list) {
        // 直接将获取到的客户列表赋值给 customerList
        // 如果API返回的是所有公司，需要在这里根据 item.isCustomer === 1 进行筛选
        customerList.value = response.list.filter(item => item.isCustomer === 1);
      } else {
        customerList.value = [];
      }
    }).catch(error => {
      message.error('搜索客户失败');
      customerList.value = [];
    }).finally(() => {
      customerLoading.value = false;
    });
  };

  // 预览并保存功能
  function handlePreviewAndSave() {
    console.log("handlePreviewAndSave 被调用");
    console.log("当前 operate.value:", operate.value);

    if (operate.value !== 2) {
      message.error("只有修改模式下才能使用预览功能");
      return;
    }

    try {
      // 获取表单数据
      let applyDataFromForm = null;
      let formulaDataFromChild = null;

      // 从顶层表单获取数据
      if (form.value) {
        applyDataFromForm = { ...form.value };
      }

      // 从子组件获取配方数据
      if (addInstance.value && typeof addInstance.value.getFormData === 'function') {
        formulaDataFromChild = addInstance.value.getFormData();
      }

      if (!applyDataFromForm) {
        message.error("获取表单数据失败");
        return;
      }

      // 构造预览数据
      const formData = {
        ...applyDataFromForm,
        quoteFormula: formulaDataFromChild
      };

      console.log("准备设置预览数据:", formData);

      // 设置预览数据
      previewData.value = formData;
      previewOpen.value = true;

      console.log("预览弹窗应该已打开, previewOpen.value:", previewOpen.value);
      console.log("预览数据:", previewData.value);

    } catch (error) {
      console.error("获取表单数据时出错:", error);
      message.error("获取表单数据时出错: " + error.message);
    }
  }

  // 确认保存功能
  function confirmSave() {
    saving.value = true;

    // 调用原有的submitForm逻辑
    try {
      // 由于submitForm不返回Promise，我们需要监听其完成状态
      // 通过监听open.value的变化来判断保存是否成功
      const originalOpen = open.value;

      // 调用submitForm
      submitForm();

      // 设置一个监听器来检测保存完成
      const unwatch = watch(open, (newVal) => {
        if (originalOpen === true && newVal === false) {
          // 对话框关闭，说明保存成功
          previewOpen.value = false;
          saving.value = false;
          unwatch(); // 取消监听
        }
      });

      // 设置超时，防止无限等待
      setTimeout(() => {
        saving.value = false;
        unwatch();
      }, 10000); // 10秒超时

    } catch (error) {
      message.error("提交失败");
      saving.value = false;
    }
  }

  // 导出函数和状态变量
  return {
    // 状态变量
    selectionType,
    selectedFormula,
    formulaOptions,
    loadingFormula,
    applyList,
    open,
    loading,
    showSearch,
    ids,
    single,
    multiple,
    total,
    title,
    addComponent,
    addInstance,
    applyId,
    refresh,
    operate,
    formulaId,
    producerId,
    producerName,
    treeSelectModel,
    form,
    queryParams: data.queryParams,
    rules: data.rules,
    companyList,
    customerOptions,
    openSupplier,
    openAdd,
    formulaSelect,
    customerList,
    addCompanyForm,
    // 基础方法
    toAddCompany,
    getList,
    remoteFormula,
    reset,
    handleQuery,
    resetQuery,
    handleSelectionChange,
    handleAdd,
    handleUpdate,
    submitForm,
    handleDelete,
    changeFormula,
    cancel,
    confirmQuote,
    getCompanyList,
    changeCustomer,
    scrollLoad,
    // 新增加工数量变更处理函数
    handleAmountChange: async () => {
      // 判断是否已选择产品规格
      if (treeSelectModel.value && form.value) {
        
        // 判断specHelpers是否可用
        if (typeof specHelpers.handleTypeSelectChange === 'function') {
          // 使用相同的方法重新查询成本，传入加工数量
          const costs = await specHelpers.handleTypeSelectChange(form.value, treeSelectModel.value, {
            quantity: form.value.amount // 加工数量作为quantity参数
          });
          
          // 更新表单成本数据
          if (costs) {
            form.value.totalPackageCost = costs.totalPackageCost;
            form.value.packagingCost = costs.totalMfgCost; // 使用totalMfgCost的值作为封装成本
            form.value.laborCost = costs.laborCost;
            form.value.utilityCost = costs.utilityCost;
            form.value.ingredientCost = costs.ingredientCost;
            form.value.totalMfgCost = costs.totalMfgCost;
            
            
            // 同步更新子组件
            if (addInstance.value && typeof addInstance.value.updateAllCosts === 'function') {
              addInstance.value.updateAllCosts({
                totalPackageCost: form.value.totalPackageCost,
                packagingCost: form.value.packagingCost,
                totalMfgCost: form.value.totalMfgCost,
                laborCost: form.value.laborCost,
                utilityCost: form.value.utilityCost,
                ingredientCost: form.value.ingredientCost
              });
            }
          }
        }
      }
    },
    // 引入的其他模块方法
    ...formulaView,
    handleExport: () => exportHelpers.handleExport(ids.value),
    exportCurrentQuote: (formTypeDisplayValue) => {
      // 1. 检查表单数据是否存在
      if (!form.value) {
        message.error("表单数据不存在，无法导出");
        return;
      }
      // 2. 检查产品规格是否已选择
      if (!treeSelectModel.value) {
        message.error("请选择产品规格后导出"); 
        return;
      }

      // 5. 调用实际的导出帮助函数，传递所需参数
      exportHelpers.exportCurrentQuote(
        addInstance.value.getFormData(),
        formulaId,         
        treeSelectModel.value, 
        formTypeDisplayValue ,
        applyId.value
      );
    },
    initSpecTreeData: specHelpers.initSpecTreeData,
    fetchMfgCost: specHelpers.fetchMfgCost,
    specTreeData: specTreeData,
    setSpecDictData,
    
    // 添加初始化方法，便于外部调用
    initialize,
    handleAmountInput: (value) => {
      // 移除对加工数量输入时的整数转换
      // if (form.value && value !== undefined) {
      //   const intValue = parseInt(value);
      //   if (!isNaN(intValue)) {
      //     form.value.amount = intValue;
      //   } else if (value === '' || isNaN(value)) {
      //     form.value.amount = '';
      //   }
      // }
      // 如果需要，可以保留或修改为其他类型的校验或格式化
      if (form.value && value !== undefined) {
        form.value.amount = value; // 直接赋值，允许小数等
      }
    },
    formulaOptions, // 配方选项
    loadingMoreFormula, // 新增：加载更多状态
    loadMoreFormula, // 新增：加载更多配方的方法

    // 预览相关
    previewOpen,
    previewData,
    saving,
    handlePreviewAndSave,
    confirmSave,
  };
}

// 暴露主函数
export default useApplyFunctions;
