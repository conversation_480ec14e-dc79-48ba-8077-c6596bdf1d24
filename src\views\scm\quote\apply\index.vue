<template>
  <ContentWrap>
    <div style="display: none;">
      <div id="dictionary-data-container"></div>
    </div>
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="客户编码" prop="customerCode">
        <el-input
          v-model="queryParams.customerCode"
          placeholder="请输入客户编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" />搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
         <el-button
            type="primary"
            plain
            @click="handleAdd"
            v-hasPermi="['quote:apply:create']"
            ><Icon icon="ep:plus" />新增</el-button
          >
          <el-button
            type="success"
            plain
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['quote:apply:update']"
            ><Icon icon="ep:edit" />修改</el-button
          >

          <el-button
            type="warning"
            plain
            @click="handleExport"
            :disabled="!ids.length"
            v-hasPermi="['quote:apply:export']"
            ><Icon icon="ep:download" />导出</el-button
          >
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="applyList"
      @selection-change="handleSelectionChange"
      row-key="id"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="客户名称" align="left" prop="customerName" show-overflow-tooltip width="180"/>
      <el-table-column
        label="产品名称"
        align="left"
        prop="productName"
        width="180"
        show-overflow-tooltip="true"
      />
      <el-table-column label="产品要求" align="left" prop="requirement" show-overflow-tooltip/>
      <el-table-column label="报价单位" align="center" prop="unit">
        <template #default="scope">
          <div style="display:flex; flex-direction:row; align-items:flex-end;">
            <div>{{ scope.row.amount }}&nbsp;</div>
            <dict-tag :type="DICT_TYPE.MEASURE_UNIT" :value="scope.row.unit" />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="报价状态"
        align="center"
        prop="status"
        width="120"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.QUOTE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="总成本" align="center" prop="totalCost" width="100"/>
      <el-table-column label="原料成本" align="center" prop="totalRawCost" width="100"/>
      <el-table-column label="包材成本" align="center" prop="totalAuxiliaryCost" />
      <el-table-column label="封装成本" align="center" prop="totalPackageCost" />
      <el-table-column label="提交人" align="center" prop="applyerName" />
      <el-table-column label="提交时间" align="center" prop="createTime" width="150" :formatter="dateFormatter"/>
      <el-table-column label="修改人" align="center" prop="updateBy" />
      <el-table-column label="修改时间" align="center" prop="updateTime" width="150" :formatter="dateFormatter"/>
      <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip/>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['quote:apply:update']"
            ><Icon icon="ep:edit" /></el-button
          >
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['quote:apply:delete']"
            v-if="scope.row.status < 2 "
            ><Icon icon="ep:delete" /></el-button
          >
          <el-button
            link
            type="primary"
            @click="handleViewFormula(scope.row)"
            v-hasPermi="['quote:apply:view']"
            v-if="scope.row.status >= 2"
            ><Icon icon="ep:view" /></el-button
          >
          <el-button
            link
            type="success"
            @click="confirmQuote(scope.row)"
            v-hasPermi="['quote:apply:confirm']"
            v-show="scope.row.status > 3 && scope.row.status != 6"
            ><Icon icon="ep:circle-check" />确认报价</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="title"
      v-model="open"
      width="60%"
      append-to-body
      fullscreen
    >
      <el-divider/>
      <el-form ref="applyRef" :model="form" :rules="rules" label-width="auto" inline>
        <el-form-item label="客户" prop="customerCode">
          <el-select 
            v-model="form.customerName"
            filterable
            remote
            placeholder="请选择客户"
            :remote-method="searchCustomer"
            style="width:300px;"
            @change="changeCustomer"
            value-key="id"
            ref="customerSelect"
            clearable
          >
            <div v-infinite-scroll="() => scrollLoad('customer')" class="infinite-scroll-container" style="max-height:200px;overflow: auto;">
              <el-option
                v-for="item in customerOptions"
                :key="item.id"
                :label="item.name"
                :value="item.name"
            >
              <div style="display: flex; flex-direction: column">
                <span>{{ item.name }}</span>
                <span style="font-size: 12px; color: #999">{{ item.shortName || '' }}</span>
              </div>
            </el-option>
            </div>
            <template #empty>
              <p class="add-company" @click="toAddCompany" v-hasPermi="['base:company:create']">
                <i class="fas fa-add"></i>增加企业
              </p>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="选择配方" prop="formulaId">
          <el-select 
            v-model="form.formulaName"
            filterable
            remote
            placeholder="请输入配方名称"
            :remote-method="remoteFormula"
            style="width: 500px;"
            @change="changeFormula"
            value-key="id"
            ref="formulaSelect"
            clearable
          >
            <div v-infinite-scroll="() => scrollLoad('formula')" class="infinite-scroll-container" style="max-height:200px;overflow: auto;">
              <el-option
                v-for="item in formulaOptions"
                :key="item.id"
                :label="`${item.name}:${item.version}`"
                :value="item"
            />
            </div>
          </el-select>
        </el-form-item>
        <el-form-item label="生产工厂" prop="producerId">
          <el-select v-model="form.producerId" placeholder="请选择生产工厂" @change="changeProducer" clearable style="width: 350px;">
            <el-option v-for="item in companyList" :key="item.id" :label="`${item.shortName} | ${item.name}`" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="产品规格">
          <el-tree-select
            v-model="treeSelectModel"
            :data="specHelpers.specTreeData.value"
            :key="specHelpers.specTreeData.value.length"
            placeholder="请选择类型"
            :render-after-expand="false"
            :props="{
              value: 'value',
              label: 'label',
              children: 'children',
            }"
            filterable
            clearable
            default-expand-all
            class="spec-tree-select"
            style="width: 300px;"
          />
          
          <div v-if="formTypeDisplay" class="selected-type-display">
            已选: {{ formTypeDisplay }}
          </div>
        </el-form-item>
        <el-form-item label="需求" prop="requirement">
          <el-input v-model="form.requirement" placeholder="请输入需求" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" style="width: 500px;"/>
        </el-form-item>
        <el-form-item label="加工数量">
          <el-input v-model="form.amount" placeholder="请输入加工数量" type="number" min="0" step="1" style="width: 200px;">
            <template #append>
              <el-tag type="primary">吨</el-tag>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <component
        :is="addComponent"
        ref="addInstance"
        :formulaId="formulaId"
        :operate="operate"
        :applyId="form.id"
        :refresh="refresh"
        :applyStatus="form.status"
        @update-success="handleSubmitSuccess"
        @submit-success="handleSubmitSuccess"
        @close-dialog="handleSubmitClose"
        :nowPage="'apply'"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button
            v-if="operate === 2"
            type="primary"
            @click="handlePreviewAndSave"
            class="submit-button"
            :loading="saving"
          >
            <Icon icon="ep:view" /> 预览并保存
          </el-button>
          <el-button
            v-else
            type="primary"
            @click="submitForm"
          >
            确 定
          </el-button>
          <el-button type="success" @click="() => exportCurrentQuote(formTypeDisplay)"
            >导出Excel</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 预览弹窗 -->
    <el-dialog
      v-model="previewOpen"
      title="报价单预览"
      width="80%"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="preview-container" style="padding: 20px;">
        <!-- 报价单基本信息 -->
        <el-descriptions
          :column="3"
          border
          class="info-descriptions compact-descriptions ultra-compact"
          :size="'default'"
          :labelStyle="{
            'font-weight': '600',
            'min-width': '90px',
            width: '90px',
            'white-space': 'nowrap',
          }"
        >
          <el-descriptions-item label="客户名称" label-width="90px" :span="2">
            <span class="important-value">{{ previewData.customerName || "未设置" }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="生产工厂">
            <span>{{ previewData.producerName || "未设置" }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="产品名称" :span="2">
            <span class="important-value">{{ previewData.productName || "未设置" }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="产品规格">
            <span>{{ getDictLabel('prod_spec', previewData.spec) || "未设置" }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="需求描述" :span="3">
            <div class="formatted-text ultra-compact-text">{{ previewData.requirement || "无" }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="加工数量">
            <span>{{ previewData.amount || "0" }} 吨</span>
          </el-descriptions-item>
          <el-descriptions-item label="总成本">
            <span>{{ previewData.totalCost || 0 }} 元</span>
          </el-descriptions-item>
          <el-descriptions-item label="原料成本">
            <span>{{ previewData.totalRawCost || 0 }} 元</span>
          </el-descriptions-item>
          <el-descriptions-item label="包材成本">
            <span>{{ previewData.totalAuxiliaryCost || 0 }} 元</span>
          </el-descriptions-item>
          <el-descriptions-item label="封装成本">
            <span>{{ previewData.totalPackageCost || 0 }} 元</span>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="1">
            <div class="formatted-text ultra-compact-text">{{ previewData.remark || "无" }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 配方信息 -->
        <div v-if="previewData.quoteFormula" style="margin-top: 24px;">
          <el-divider content-position="left">
            <Icon icon="ep:document" style="margin-right: 8px;" />
            配方信息
          </el-divider>

          <!-- 配方基本信息 -->
          <el-descriptions
            :column="3"
            border
            class="info-descriptions compact-descriptions ultra-compact"
            style="margin-bottom: 16px;"
            :labelStyle="{
              'font-weight': '600',
              'min-width': '90px',
              width: '90px',
              'white-space': 'nowrap',
            }"
          >
            <el-descriptions-item label="配方名称" :span="2">
              <span class="important-value">{{ previewData.quoteFormula.name || "未设置" }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="版本">
              <span>{{ previewData.quoteFormula.version || "未设置" }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="企业">
              <span>{{ previewData.quoteFormula.companyName || "未设置" }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="产品形态">
              <dict-tag
                v-if="previewData.quoteFormula.stateCode"
                :type="DICT_TYPE.PRODUCT_STATE"
                :value="previewData.quoteFormula.stateCode"
                size="small"
              />
              <span v-else class="empty-value">未设置</span>
            </el-descriptions-item>
            <el-descriptions-item label="通用名">
              <dict-tag
                v-if="previewData.quoteFormula.typeCode"
                :type="DICT_TYPE.PRODUCT_CATEGORY"
                :value="previewData.quoteFormula.typeCode"
                size="small"
              />
              <span v-else class="empty-value">未设置</span>
            </el-descriptions-item>
            <el-descriptions-item label="氮磷钾含量" :span="3">
              <div class="npk-preview-container">
                <span v-if="previewData.quoteFormula.npk && (previewData.quoteFormula.npk.N || previewData.quoteFormula.npk.P || previewData.quoteFormula.npk.K)" class="npk-content">
                  <el-tag v-if="previewData.quoteFormula.npk.N" type="info" size="small" class="npk-tag">
                    N: {{ previewData.quoteFormula.npk.N }}{{ previewData.quoteFormula.npk.n_unit || '%' }}
                  </el-tag>
                  <el-tag v-if="previewData.quoteFormula.npk.P" type="info" size="small" class="npk-tag">
                    {{ previewData.quoteFormula.npk.PType || 'P' }}: {{ previewData.quoteFormula.npk.P }}{{ previewData.quoteFormula.npk.p_unit || '%' }}
                  </el-tag>
                  <el-tag v-if="previewData.quoteFormula.npk.K" type="info" size="small" class="npk-tag">
                    K: {{ previewData.quoteFormula.npk.K }}{{ previewData.quoteFormula.npk.k_unit || '%'}}
                  </el-tag>
                </span>
                <span v-else class="empty-value">未设置</span>
              </div>
            </el-descriptions-item>

            <!-- 中微量元素 -->
            <el-descriptions-item label="中微量元素" :span="1">
              <div class="elements-preview-container" v-if="filteredMicroElements.length > 0">
                <el-tag
                  v-for="(item, index) in filteredMicroElements"
                  :key="index"
                  type="info"
                  effect="light"
                  size="small"
                  class="element-tag"
                >
                  {{ getDictLabel('medium_trace_element', item.element) }}: {{ item.quantity }}{{ item.unit }}
                </el-tag>
              </div>
              <span v-else class="empty-value">无</span>
            </el-descriptions-item>

            <!-- 其他原料 -->
            <el-descriptions-item label="其他原料" :span="2">
              <div class="elements-preview-container" v-if="filteredOtherMaterials.length > 0">
                <el-tag
                  v-for="(item, index) in filteredOtherMaterials"
                  :key="index"
                  type="info"
                  effect="light"
                  size="small"
                  class="element-tag"
                >
                  {{ getDictLabel('product_name_abbr', item.element) }}: {{ item.quantity }}{{ item.unit }}
                </el-tag>
              </div>
              <span v-else class="empty-value">无</span>
            </el-descriptions-item>

            <!-- 成本信息 -->
            <el-descriptions-item label="总成本">
              <span>{{ Number(previewData.quoteFormula.totalCost || 0).toFixed(2) }} 元</span>
            </el-descriptions-item>
            <el-descriptions-item label="原料成本">
              <span>{{ Number(previewData.quoteFormula.totalRawCost || 0).toFixed(2) }} 元</span>
            </el-descriptions-item>
            <el-descriptions-item label="包装成本">
              <span>{{ Number(previewData.quoteFormula.totalAuxiliaryCost || 0).toFixed(2) }} 元</span>
            </el-descriptions-item>

            <!-- 物理属性 -->
            <el-descriptions-item label="密度" v-if="previewData.quoteFormula.density">
              <span>{{ previewData.quoteFormula.density }} g/cm³</span>
            </el-descriptions-item>
            <el-descriptions-item label="pH值" v-if="previewData.quoteFormula.ph">
              <span>{{ previewData.quoteFormula.ph }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="EC值" v-if="previewData.quoteFormula.ec">
              <span>{{ previewData.quoteFormula.ec }} mS/cm</span>
            </el-descriptions-item>

            <!-- 工艺说明 -->
            <el-descriptions-item label="工艺说明" :span="2" v-if="previewData.quoteFormula.processDesc">
              <div class="formatted-text ultra-compact-text">{{ previewData.quoteFormula.processDesc }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="配方备注" :span="1" v-if="previewData.quoteFormula.remark">
              <div class="formatted-text ultra-compact-text">{{ previewData.quoteFormula.remark }}</div>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 配方材料清单 -->
          <div v-if="previewData.quoteFormula.materials && previewData.quoteFormula.materials.filter(m => m.materialName).length > 0">
            <el-divider content-position="left">
              <Icon icon="ep:list" style="margin-right: 8px;" />
              配方材料清单
            </el-divider>
            <el-table
              :data="previewData.quoteFormula.materials.filter(m => m.materialName)"
              border
              style="width: 100%; margin-top: 16px;"
              size="small"
              :summary-method="summaryMethod"
              show-summary
            >
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="materialName" label="原料名称" min-width="200" show-overflow-tooltip />
              <el-table-column prop="materialCode" label="原料编码" width="120" show-overflow-tooltip />
              <el-table-column prop="amount" label="投入份数" width="100" align="center">
                <template #default="scope">
                  <span class="amount-value">{{ Number(scope.row.amount || 0).toFixed(2) }} 份</span>
                </template>
              </el-table-column>
              <el-table-column prop="price" label="单价" width="120" align="right">
                <template #default="scope">
                  <span class="price-value">{{ Number(scope.row.price || 0).toFixed(4) }} {{ scope.row.priceUnit || "元" }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="cost" label="投入成本" width="140" align="right">
                <template #default="scope">
                  <span class="cost-value">{{ Number(scope.row.cost || 0).toFixed(4) }} {{ scope.row.priceUnit || "元" }}</span>
                </template>
              </el-table-column>
              <el-table-column label="材料类型" width="100" align="center">
                <template #default="scope">
                  <el-tag
                    type="info"
                    size="small"
                  >
                    {{ scope.row.materialType === '4' ? '包装材料' : '原料' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 包装材料清单 -->
          <div v-if="previewData.quoteFormula.packages && previewData.quoteFormula.packages.filter(p => p.materialName).length > 0">
            <el-divider content-position="left">
              <Icon icon="ep:box" style="margin-right: 8px;" />
              包装材料清单
            </el-divider>
            <el-table
              :data="previewData.quoteFormula.packages.filter(p => p.materialName)"
              border
              style="width: 100%; margin-top: 16px;"
              size="small"
            >
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="materialName" label="包装材料名称" min-width="200" show-overflow-tooltip />
              <el-table-column prop="materialCode" label="材料编码" width="120" show-overflow-tooltip />
              <el-table-column prop="amount" label="数量" width="120" align="center">
                <template #default="scope">
                  <span class="amount-value">{{ Number(scope.row.amount || 0) }} {{ scope.row.amountUnit || "个" }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="price" label="单价" width="120" align="right">
                <template #default="scope">
                  <span class="price-value">{{ Number(scope.row.price || 0).toFixed(4) }} {{ scope.row.priceUnit || "元" }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="cost" label="成本" width="140" align="right">
                <template #default="scope">
                  <span class="cost-value">{{ Number(scope.row.cost || 0).toFixed(4) }} {{ scope.row.priceUnit || "元" }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="previewOpen = false" class="cancel-button">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="confirmSave"
            :loading="saving"
            class="submit-button"
          >
            <Icon icon="ep:check" /> 确认保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增查看配方弹窗 -->
    <el-dialog
      :title="'查看配方：' + (formulaDetail.name || '')"
      v-model="formulaViewOpen"
      width="80%"
      append-to-body
      fullscreen
      destroy-on-close
      class="formula-dialog"
      :class="{
        'packages-active': activeFormulaTab === 'packages',
        'detail-active': activeFormulaTab === 'detail',
        'compact-layout': activeFormulaTab !== 'basic',
        'narrow-table': isTableNarrow
      }"
    >
      <div v-loading="formulaLoading">
        <!-- 配方基本信息 -->
        <el-tabs v-model="activeFormulaTab">
          <el-tab-pane label="基本信息" name="basic">
            <!-- 基本信息卡片 -->
            <el-card class="box-card basic-info-card">
              <template #header>
                <div class="card-header">
                  <i class="el-icon-info"></i>
                  <span>配方基本信息</span>
                </div>
              </template>

              <el-descriptions
                :column="2"
                border
                class="info-descriptions"
                :size="'default'"
                :labelStyle="{
                  'font-weight': '600',
                  'min-width': '120px',
                  width: '150px',
                  'white-space': 'nowrap',
                }"
              >
                <el-descriptions-item label="配方名称" label-width="150px">
                  <span class="important-value">{{
                    applyDetail.productName || "未设置"
                  }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="产品形态">
                  <dict-tag
                    v-if="formulaDetail.stateCode"
                    :type="DICT_TYPE.PRODUCT_STATE"
                    :value="formulaDetail.stateCode"
                  />
                  <span v-else class="empty-value">未设置</span>
                </el-descriptions-item>
                <el-descriptions-item label="通用名">
                  <dict-tag
                    v-if="formulaDetail.typeCode"
                    :type="DICT_TYPE.PRODUCT_CATEGORY"
                    :value="formulaDetail.typeCode"
                  />
                  <span v-else class="empty-value">未设置</span>
                </el-descriptions-item>
                <el-descriptions-item label="客户">
                  <span>{{ applyDetail.customerName || "未设置" }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="规格">
                  <span>{{ getDictLabel('prod_spec', applyDetail.spec) || "未设置" }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="氮磷钾含量" :span="2">
                  <div class="npk-container">
                    <el-tag
                      class="npk-tag"
                      v-if="formulaDetail.npk && formulaDetail.npk.N"
                    >
                      N: {{ formulaDetail.npk.N }}{{ formulaDetail.npk.n_unit || '%' }}
                    </el-tag>
                    <el-tag
                      class="npk-tag"
                      v-if="formulaDetail.npk && formulaDetail.npk.P"
                    >
                      {{ formulaDetail.npk.PType || "P" }}: {{ formulaDetail.npk.P }}{{ formulaDetail.npk.p_unit || '%' }}
                    </el-tag>
                    <el-tag
                      class="npk-tag"
                      v-if="formulaDetail.npk && formulaDetail.npk.K"
                    >
                      K: {{ formulaDetail.npk.K }}{{ formulaDetail.npk.k_unit || '%'}}
                    </el-tag>
                    <span
                      v-if="
                        !formulaDetail.npk || (!formulaDetail.npk.N && !formulaDetail.npk.P && !formulaDetail.npk.K)
                      "
                      class="empty-value"
                      >未设置</span
                    >
                  </div>
                </el-descriptions-item>
                <!-- 中微量元素含量 - 改进显示方式 -->
                <el-descriptions-item label="中微量元素含量" :span="2">
                  <el-space wrap v-if="filteredMicroElements.length > 0">
                    <el-tag
                      v-for="(item, index) in filteredMicroElements"
                      :key="index"
                      type="info"
                      effect="light"
                      class="element-tag micro-element-tag"
                    >
                      <span class="element-name">{{ getDictLabel('medium_trace_element',item.element) }}:</span>
                      <span class="element-value"
                        >{{ item.quantity }} {{ item.unit }}</span
                      >
                    </el-tag>
                  </el-space>
                  <span v-else class="empty-value">无</span>
                </el-descriptions-item>

                <!-- 其他原料 - 改进显示方式 -->
                <el-descriptions-item label="其他原料" :span="2">
                  <el-space wrap v-if="filteredOtherMaterials.length > 0">
                    <el-tag
                      v-for="(item, index) in filteredOtherMaterials"
                      :key="index"
                      type="success"
                      effect="light"
                      class="element-tag other-material-tag"
                    >
                      <span class="element-name">{{ getDictLabel('product_name_abbr',item.element) }}:</span>
                      <span class="element-value"
                        >{{ item.quantity }} {{ item.unit }}</span
                      >
                    </el-tag>
                  </el-space>
                  <span v-else class="empty-value">无</span>
                </el-descriptions-item>

                <el-descriptions-item label="工艺说明" :span="2">
                  <div class="formatted-text">{{ formulaDetail.processDesc || "无" }}</div>
                </el-descriptions-item>
                <el-descriptions-item label="备注" :span="2">
                  <div class="formatted-text">{{ formulaDetail.remark || "无" }}</div>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="配方明细" name="detail">
            <!-- 配方明细区域 -->
            <div class="detail-layout-container">
              <el-card class="box-card detail-card" :class="{ 'narrow-table-card': isTableNarrow }">
                <template #header>
                  <div class="card-header">
                    <i class="el-icon-document"></i>
                    <span>配方材料清单</span>
                  </div>
                </template>

                <!-- 自适应布局容器 -->
                <div class="adaptive-layout-container">
                  <!-- 表格区域 -->
                  <div class="table-section">
                    <!-- 表格水平滚动容器 -->
                    <div class="table-container">
                      <el-table
                        :data="formulaDetail.materials"
                        border
                        show-summary
                        :summary-method="summaryMethod"
                        style="width: 100%"
                        class="detail-table"
                        :header-cell-style="{
                          background: '#f5f7fa',
                          color: '#303133',
                          fontWeight: 'bold',
                        }"
                        empty-text="暂无配方明细数据"
                      >
                      <el-table-column
                        type="index"
                        label="序号"
                        width="80"
                        align="center"
                        fixed="left"
                      />
                      <el-table-column
                        prop="materialName"
                        label="原料名称"
                        align="center"
                        width="180"
                        fixed="left"
                      >
                        <template #default="{ row }">
                          <span>{{ row.materialName || "未命名原料" }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="投入份数"
                        prop="amount"
                        align="center"
                        width="100"
                      >
                        <template #default="{ row }">
                          <span>{{ row.amount || "0" }} 份</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        v-for="ele in Object.keys(formulaDetail.elements || {}).filter(key => key !== 'N+P+K')"
                        :key="ele"
                        :label="ele"
                        align="center"
                        width="100"
                      >
                        <template #default="{ row }">
                          <span>{{ (row.element && row.element[ele]) || "0" }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="单价" prop="price" align="center" width="130" show-overflow-tooltip="">
                        <template #default="{ row }">
                          <span>{{ row.price || "0" }} {{ row.priceUnit || "" }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="投入成本"
                        prop="cost"
                        align="center"
                        width="140"
                        fixed="right"
                        show-overflow-tooltip
                      >
                        <template #default="{ row }">
                          <span>{{ row.cost || "0" }} {{ row.priceUnit || "" }}</span>
                        </template>
                      </el-table-column>
                      </el-table>
                    </div>
                  </div>
                </div>
              </el-card>

              <!-- 配方汇总信息卡片 - 配方明细tab页右侧显示 -->
              <div v-if="activeFormulaTab === 'detail'" class="right-summary-card">
                <el-card class="summary-info-card right-summary-style">
                  <el-divider content-position="left">
                    <el-text type="primary" class="divider-title">
                      <i class="el-icon-data-analysis"></i>
                      配方汇总信息
                    </el-text>
                  </el-divider>

                  <!-- 汇总信息卡片布局 -->
                  <el-row :gutter="10" class="stats-row">
                    <el-col :span="24">
                      <div class="stat-card">
                        <el-statistic
                          title="投入总份数"
                          :value="Number(formulaDetail.totalAmount || 0)"
                          :precision="4"
                        >
                          <template #suffix>
                            <span class="stat-unit">份</span>
                          </template>
                        </el-statistic>
                      </div>
                    </el-col>
                    <el-col :span="24">
                      <div class="stat-card">
                        <el-statistic
                          title="原料总成本"
                          :value="Number(applyDetail.totalRawCost || 0)"
                          :precision="4"
                        >
                          <template #suffix>
                            <span class="stat-unit">元</span>
                          </template>
                        </el-statistic>
                      </div>
                    </el-col>
                    <el-col :span="24">
                      <div class="stat-card">
                        <el-statistic
                          title="封装成本"
                          :value="Number(applyDetail.totalPackageCost || 0)"
                          :precision="4"
                        >
                          <template #suffix>
                            <span class="stat-unit">元</span>
                          </template>
                        </el-statistic>
                      </div>
                    </el-col>
                    <el-col :span="24">
                      <div class="stat-card">
                        <el-statistic
                          title="包装成本"
                          :value="Number(applyDetail.totalAuxiliaryCost || 0)"
                          :precision="4"
                        >
                          <template #suffix>
                            <span class="stat-unit">元</span>
                          </template>
                        </el-statistic>
                      </div>
                    </el-col>
                    <el-col :span="24">
                      <div class="stat-card">
                        <el-statistic
                          title="总成本"
                          :value="Number(applyDetail.totalCost || 0)"
                          :precision="4"
                        >
                          <template #suffix>
                            <span class="stat-unit">元</span>
                          </template>
                        </el-statistic>
                      </div>
                    </el-col>
                  </el-row>

                  <!-- 元素含量汇总区域 - 只在有元素数据且元素数量大于0时显示 -->
                  <div
                      v-if="formulaDetail.elements &&
                      Object.keys(formulaDetail.elements).length > 0 &&
                      Object.keys(formulaDetail.elements).filter(key => key !== 'N+P+K').length > 0 &&
                      (formulaDetail.elements['N+P+K'] > 0 ||
                        Object.entries(formulaDetail.elements).some(([key, value]) => key !== 'N+P+K' && value > 0))">
                    <el-divider content-position="left">
                      <el-text type="primary" class="divider-title">
                        <i class="el-icon-pie-chart"></i>
                        元素含量汇总
                      </el-text>
                    </el-divider>

                    <el-row :gutter="20" class="stats-row">
                      <el-col :span="24">
                        <el-table
                          :data="formulaElementTableData"
                          border
                          stripe
                          class="elements-table"
                          :cell-style="{ textAlign: 'center' }"
                          :header-cell-style="{
                            backgroundColor: '#f5f7fa',
                            color: '#303133',
                            fontWeight: '500',
                            textAlign: 'center',
                          }"
                        >
                          <el-table-column
                            v-for="column in formulaElementColumns"
                            :key="column.prop"
                            :prop="column.prop"
                            :label="column.prop"
                          >
                            <template #default="scope">
                              <span class="element-value"
                                >{{ scope.row[column.prop] }}%</span
                              >
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-col>
                    </el-row>
                  </div>
                </el-card>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="包装明细" name="packages">
            <!-- 包装明细区域 -->
            <div class="detail-layout-container">
              <el-card class="box-card detail-card narrow-table-card package-card">
                <template #header>
                  <div class="card-header">
                    <i class="el-icon-document"></i>
                    <span>包装材料清单</span>
                  </div>
                </template>

                <!-- 自适应布局容器 -->
                <div class="adaptive-layout-container">
                  <!-- 表格区域 -->
                  <div class="table-section">
                    <!-- 表格水平滚动容器 -->
                    <div class="table-container">
                      <el-table
                        :data="formulaDetail.packages"
                        border
                        show-summary
                        :summary-method="summaryPackageMethod"
                        style="width: 100%"
                        class="detail-table package-table"
                        :header-cell-style="{
                          background: '#f5f7fa',
                          color: '#303133',
                          fontWeight: 'bold',
                        }"
                        empty-text="暂无包装材料数据"
                      >
                      <el-table-column
                        type="index"
                        label="序号"
                        width="80"
                        align="center"
                        fixed="left"
                      />
                      <el-table-column
                        prop="materialName"
                        label="包装材料名称"
                        align="center"
                        width="200"
                        fixed="left"
                        show-overflow-tooltip
                      >
                        <template #default="{ row }">
                          <span>{{ row.materialName || "未命名包材" }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="数量"
                        prop="amount"
                        align="center"
                        width="120"
                      >
                        <template #default="{ row }">
                          <span>{{ row.amount || "0" }} {{ row.amountUnit || "个" }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="单价" prop="price" align="center" width="120" show-overflow-tooltip>
                        <template #default="{ row }">
                          <span>{{ Number(row.price || 0).toFixed(4) }} {{ row.priceUnit || "" }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="成本"
                        prop="cost"
                        align="center"
                        width="140"
                        fixed="right"
                        show-overflow-tooltip
                      >
                        <template #default="{ row }">
                          <span>{{ Number(row.cost || 0).toFixed(4) }} {{ row.priceUnit || "" }}</span>
                        </template>
                      </el-table-column>
                      </el-table>
                    </div>
                  </div>
                </div>
              </el-card>

              <!-- 配方汇总信息卡片 - 包装明细tab页右侧显示 -->
              <div v-if="activeFormulaTab === 'packages'" class="right-summary-card">
                <el-card class="summary-info-card right-summary-style">
                  <el-divider content-position="left">
                    <el-text type="primary" class="divider-title">
                      <i class="el-icon-data-analysis"></i>
                      配方汇总信息
                    </el-text>
                  </el-divider>

                  <!-- 汇总信息卡片布局 -->
                  <el-row :gutter="10" class="stats-row">
                    <el-col :span="24">
                      <div class="stat-card">
                        <el-statistic
                          title="投入总份数"
                          :value="Number(formulaDetail.totalAmount || 0)"
                          :precision="4"
                        >
                          <template #suffix>
                            <span class="stat-unit">份</span>
                          </template>
                        </el-statistic>
                      </div>
                    </el-col>
                    <el-col :span="24">
                      <div class="stat-card">
                        <el-statistic
                          title="原料总成本"
                          :value="Number(applyDetail.totalRawCost || 0)"
                          :precision="4"
                        >
                          <template #suffix>
                            <span class="stat-unit">元</span>
                          </template>
                        </el-statistic>
                      </div>
                    </el-col>
                    <el-col :span="24">
                      <div class="stat-card">
                        <el-statistic
                          title="封装成本"
                          :value="Number(applyDetail.totalPackageCost || 0)"
                          :precision="4"
                        >
                          <template #suffix>
                            <span class="stat-unit">元</span>
                          </template>
                        </el-statistic>
                      </div>
                    </el-col>
                    <el-col :span="24">
                      <div class="stat-card">
                        <el-statistic
                          title="包装成本"
                          :value="Number(applyDetail.totalAuxiliaryCost || 0)"
                          :precision="4"
                        >
                          <template #suffix>
                            <span class="stat-unit">元</span>
                          </template>
                        </el-statistic>
                      </div>
                    </el-col>
                    <el-col :span="24">
                      <div class="stat-card">
                        <el-statistic
                          title="总成本"
                          :value="Number(applyDetail.totalCost || 0)"
                          :precision="4"
                        >
                          <template #suffix>
                            <span class="stat-unit">元</span>
                          </template>
                        </el-statistic>
                      </div>
                    </el-col>
                  </el-row>
                </el-card>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 配方明细tab页的汇总信息 -->
          <el-tab-pane v-if="false" name="detail-summary" />
          
          <!-- 包装明细tab页的汇总信息 -->  
          <el-tab-pane v-if="false" name="packages-summary" />
        </el-tabs>
        

      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="success" @click="exportFormulaExcel"
            >导出Excel</el-button
          >
          <el-button @click="formulaViewOpen = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog title="管理客户企业" v-model="openSupplier" width="600px" append-to-body fullscreen>
      <component
        :is="company"
        :company="addCompanyForm"
        :openAdd="openAdd"
        :key="openSupplier"
        @close="handleCloseSupplier"
      />
    </el-dialog>
  </ContentWrap>
</template>

<script setup name="Apply">

import { useApplyFunctions } from "./index.js";
import { getCurrentInstance, watch, onMounted, ref, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import company from '../../base/company/index.vue'
import { useSpecHelpers } from "./spec-helpers";
import { CompanyApi } from "@/api/scm/base/company";
// 导入 Icon 组件
import Icon from '@/components/Icon/src/Icon.vue';
import { dateFormatter } from '@/utils/formatTime'
import { getDictLabel } from "@/utils/dict";
// 从环境变量中获取DEV标志
const isDev = ref(import.meta.env.DEV);
// 导入字典类型常量
import { getStrDictOptions, getBoolDictOptions, DICT_TYPE } from '@/utils/dict'

const { proxy } = getCurrentInstance();
const applyRef = ref(null);

// 客户远程搜索方法
const searchCustomer = (query) => {
  if (!query) return;
  // 使用代理发起请求，假设有一个已经定义的用于获取客户数据的API
  CompanyApi.getCompanyPage({
    pageNo: 1,
    pageSize: 10,
    name: query, // 使用搜索关键词
    isCustomer: 1 // 筛选客户类型
  }).then(response => {
    // 更新客户选项数据
    customerList.value = response.list;
  })
};

// 获取路由
const route = useRoute();
const router = useRouter();
const quoteId = route.query.id || null;
const viewDetailTab = ref('materials')

// 智能检测表格是否较窄，决定是否使用右侧布局
const isTableNarrow = computed(() => {
  if (!activeFormulaTab || activeFormulaTab.value === 'basic') {
    return false;
  }

  // 包装明细表格始终较窄（固定680px）
  if (activeFormulaTab.value === 'packages') {
    return true;
  }

  // 配方明细表格 - 检查是否有较少的元素列
  if (activeFormulaTab.value === 'detail') {
    const elementCount = formulaDetail.value?.elements ?
      Object.keys(formulaDetail.value.elements).filter(key => key !== 'N+P+K').length : 0;
    const materialCount = formulaDetail.value?.materials?.length || 0;

    // 计算表格预估宽度
    // 基础列宽度：序号(80) + 原料名称(180) + 投入份数(100) + 单价(130) + 投入成本(140) = 630px
    const baseWidth = 630;
    // 元素列宽度：每列100px
    const elementWidth = elementCount * 100;
    const estimatedTableWidth = baseWidth + elementWidth;

    // 如果预估表格宽度小于900px，认为表格较窄，可以使用右侧布局
    return estimatedTableWidth < 900;
  }

  return false;
})

// 包装明细汇总方法
const summaryPackageMethod = (params) => {
  const { columns, data } = params;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    
    if (column.property === 'amount') {
      const values = data.map(item => Number(item.amount || 0));
      sums[index] = values.reduce((prev, curr) => prev + curr, 0);
    } else if (column.property === 'cost') {
      const values = data.map(item => Number(item.cost || 0));
      sums[index] = values.reduce((prev, curr) => prev + curr, 0).toFixed(4);
    } else {
      sums[index] = '';
    }
  });
  return sums;
}

// 导入拆分后的functions
const {
  // 状态变量
  selectionType,
  selectedFormula,
  openAdd,
  openSupplier,
  formulaOptions,
  loadingFormula,
  applyList,
  open,
  loading,
  showSearch,
  ids,
  single,
  multiple,
  total,
  title,
  addComponent,
  addInstance,
  applyId,
  refresh,
  operate,
  formulaId,
  producerId,
  producerName,
  treeSelectModel,
  form,
  queryParams,
  rules,
  companyList,
  customerOptions,
  formulaSelect,
  customerList,
  applyDetail,
  addCompanyForm,
  // 基本方法
  getList,
  remoteFormula,
  reset,
  handleQuery,
  resetQuery,
  handleSelectionChange,
  handleAdd,
  handleUpdate,
  submitForm,
  handleDelete,
  changeFormula,
  cancel,
  confirmQuote,
  getCompanyList,
  changeCustomer,
  toAddCompany,
  scrollLoad,
  // 配方查看相关
  formulaViewOpen,
  formulaLoading,
  activeFormulaTab,
  formulaDetail,
  formulaMaterials,
  formulaPackagingMaterials,
  formulaDetailMicroElements,
  formulaDetailOtherMaterials,
  formulaElementTableData,
  formulaElementColumns,
  handleViewFormula,
  getPackageCost,
  summaryMethod,
  calculateUnitCost,
  exportFormulaExcel,

  // 导出功能相关
  handleExport,
  exportCurrentQuote,

  // 产品规格相关
  specTreeData,
  initSpecTreeData,

  // 预览相关
  previewOpen,
  previewData,
  saving,
  handlePreviewAndSave,
  confirmSave,
} = useApplyFunctions(proxy);

// 获取规格助手
const specHelpers = useSpecHelpers(proxy);

// 监听 treeSelectModel 的变化
watch(treeSelectModel, async (newValue) => {
  
  // 增加对 form.value 的检查
  if (!form.value) {
    return;
  }
  
  if (typeof specHelpers.handleTypeSelectChange === 'function') {
    const costs = await specHelpers.handleTypeSelectChange(form.value, newValue);
    if(costs){
      form.value.totalPackageCost = costs.totalMfgCost.toFixed(4)
    }
    // 解析规格值，设置到表单中
    if (newValue) {
      // 规格通常格式：type-subType-spec，例如：powder-1-20kg_bag
      const parts = newValue.split('-');
      if (parts.length >= 1) {
        form.value.type = parts[0]; // 例如：powder
      }
      
      if (parts.length >= 2) {
        form.value.subType = parts[1]; // 例如：1 (表示腐植酸)
      }
      
      if (parts.length >= 3) {
        form.value.spec = parts[2]; // 例如：20kg_bag
      }
    }
  }
});

// 过滤中微量元素数据 - 支持预览数据和配方详情数据
const filteredMicroElements = computed(() => {
  // 优先使用预览数据，如果没有则使用配方详情数据
  const sourceData = previewData.value?.quoteFormula?.microElement || formulaDetail.value?.microElement;

  if (!sourceData || !Array.isArray(sourceData)) {
    return []
  }
  return sourceData.filter(item =>
    item.element &&
    item.quantity !== null &&
    item.quantity !== undefined &&
    item.quantity !== '' &&
    item.quantity !== '0' &&
    item.quantity !== 0 &&
    item.unit
  )
})

// 过滤其他原料数据 - 支持预览数据和配方详情数据
const filteredOtherMaterials = computed(() => {
  // 优先使用预览数据，如果没有则使用配方详情数据
  const sourceData = previewData.value?.quoteFormula?.otherMaterial || formulaDetail.value?.otherMaterial;

  if (!sourceData || !Array.isArray(sourceData)) {
    return []
  }
  return sourceData.filter(item =>
    item.element &&
    item.quantity !== null &&
    item.quantity !== undefined &&
    item.quantity !== '' &&
    item.quantity !== '0' &&
    item.quantity !== 0 &&
    item.unit
  )
})

// 在 index.vue 中定义 formTypeDisplay 计算属性
const formTypeDisplay = computed(() => {
  const selectedValue = treeSelectModel.value;
  if (!selectedValue) return null;

  // 查找对应的节点
  let foundNode = null;
  const treeData = specHelpers.specTreeData.value || [];

  function findNode(nodes, valueToFind) {
    for (const node of nodes) {
      if (node.value === valueToFind) {
        return node;
      }
      if (node.children) {
        const foundInChildren = findNode(node.children, valueToFind);
        if (foundInChildren) return foundInChildren;
      }
    }
    return null;
  }

  foundNode = findNode(treeData, selectedValue);

  if (foundNode) {
    // 如果找到了节点，显示其 label
    const nodeLabel = foundNode.label || '';
    
    // 尝试获取更高级别的父节点类型信息
    let typeInfo = '';
    let subTypeInfo = '';
    
    // --- 确定 typeInfo --- 
    // 优先从节点自身属性获取
    if (foundNode.type) {
      typeInfo = foundNode.type === 'powder' ? '粉剂' : (foundNode.type === 'liquid' ? '液体' : foundNode.type);
    } 
    // 其次从节点 value 解析
    else if (typeof foundNode.value === 'string' && foundNode.value.includes('-')) {
      const typePart = foundNode.value.split('-')[0];
      typeInfo = typePart === 'powder' ? '粉剂' : (typePart === 'liquid' ? '液体' : typePart);
    }
    // 最后通过父节点判断
    else {
      function findParentNode(nodes, childValue, parent = null) {
        for (const node of nodes) {
          if (node.value === childValue) return parent;
          if (node.children) {
            const foundParent = findParentNode(node.children, childValue, node);
            if (foundParent) return foundParent;
          }
        }
        return null;
      }
      const parentNode = findParentNode(treeData, selectedValue);
      if (parentNode && (parentNode.value === 'powder' || parentNode.value === 'liquid')) {
        typeInfo = parentNode.label;
      }
    }

    // --- 确定 subTypeInfo (仅当 type 是粉剂时) ---
    if (typeInfo === '粉剂') { 
      // 优先从节点自身属性获取
      if (foundNode.subType) {
        subTypeInfo = foundNode.subType === '1' ? '腐植酸' : '非腐植酸';
      } 
      // 其次从节点 value 解析
      else if (typeof foundNode.value === 'string' && foundNode.value.includes('-')) {
        const parts = foundNode.value.split('-');
        if (parts.length >= 2) {
          subTypeInfo = parts[1] === '1' ? '腐植酸' : '非腐植酸';
        }
      }
      // 最后通过父节点判断
      else {
        function findParentNode(nodes, childValue, parent = null) { // 重复定义是为了作用域
          for (const node of nodes) {
            if (node.value === childValue) return parent;
            if (node.children) {
              const foundParent = findParentNode(node.children, childValue, node);
              if (foundParent) return foundParent;
            }
          }
          return null;
        }
        const parentNode = findParentNode(treeData, selectedValue);
        // 如果父节点是子类型节点 (腐植酸/非腐植酸)
        if (parentNode && (parentNode.value === '1' || parentNode.value === '0')) {
           subTypeInfo = parentNode.label;
        }
      }
    } // 如果类型不是粉剂，subTypeInfo 保持为空
    
    // --- 组装最终显示文本 --- 
    if (typeInfo && subTypeInfo) { // 仅当类型是粉剂且有子类型时显示三段
      return `${typeInfo} | ${subTypeInfo} | ${nodeLabel}`;
    } else if (typeInfo) { // 其他情况（如液体，或只有类型的粉剂）显示两段
      return `${typeInfo} | ${nodeLabel}`;
    } else { // 如果连类型都无法确定，只显示规格本身
      return nodeLabel;
    }
  } else {
    // 如果在树中找不到，可能是旧数据或意外情况，直接显示原始值
    return selectedValue;
  }
});

// 在onMounted监听器中添加响应自定义事件的代码
onMounted(async () => {
  // 初始化分类树数据
  try {
    // 设置字典数据
    await specHelpers.setDictionaryData(
      DICT_TYPE.PROD_SPEC,
      DICT_TYPE.PROD_SPEC_SUB_TYPE,
      DICT_TYPE.MATERIAL_STATE
    );
    
    // 将规格树数据赋值给当前组件的数据
    if (specTreeData && typeof specTreeData === 'object') {
      specTreeData.value = specHelpers.specTreeData.value;
    }
    
    // 检查是否有用户提供的成本数据
    if (window.mfgCostData && Array.isArray(window.mfgCostData) && window.mfgCostData.length > 0) {
      // 从全局变量获取成本数据并构建树
      await buildSpecTreeFromProvidedCostData(window.mfgCostData);
    }
  } catch (error) {
  }

  // 检查是否有全局传入的字典数据需要处理
  if (window.dictDataToProcess) {
    try {
      const data = window.dictDataToProcess;
      
      // 处理产品规格数据
      if (data.prodSpec && Array.isArray(data.prodSpec)) {
        // 标准化字典项属性
        const normalizedProdSpec = data.prodSpec.map(item => {
          // 确保每个项都有统一的属性名
          return {
            dictValue: item.dictValue || item.value,
            dictLabel: item.dictLabel || item.label,
            dictCode: item.dictCode || item.code,
            parentValue: item.parentValue,
            parentCode: item.parentCode
          };
        });
        
        // 处理规格子类型和材料状态数据
        const prodSpecSubType = data.prodSpecSubType || [];
        const materialState = data.materialState || [];
        
        // 使用spec-helpers中的方法设置字典数据
        const result = await specHelpers.setDictionaryData(normalizedProdSpec, prodSpecSubType, materialState);
      }
      
      // 处理完成后清除全局变量
      delete window.dictDataToProcess;
    } catch (error) {
    }
  }
  
  // 监听自定义事件，用于接收动态传入的字典数据
  window.addEventListener("process-dict-data", (event) => {
    try {
      if (event.detail && event.detail.data) {
        const data = event.detail.data;
        
        // 使用spec-helpers中的方法设置字典数据
        specHelpers.setDictionaryData(
          data.prodSpec || [], 
          data.prodSpecSubType || [], 
          data.materialState || []
        ).then(result => {
          // 如果有回调函数，则调用
          if (event.detail.callback && typeof event.detail.callback === "function") {
            event.detail.callback(result);
          }
        });
      }
    } catch (error) {
    }
  });
  
  // 添加自执行函数来立即处理可能存在的数据
  (async function processExistingDictData() {
    // 尝试从隐藏元素中获取数据
    const dictDataElement = document.getElementById("hidden-dict-data");
    if (dictDataElement && dictDataElement.textContent) {
      try {
        const data = JSON.parse(dictDataElement.textContent);
        
        // 使用spec-helpers中的方法设置字典数据
        await specHelpers.setDictionaryData(
          data.prodSpec || [], 
          data.prodSpecSubType || [], 
          data.materialState || []
        );
      } catch (error) {
      }
    }
  })();

  watch(specHelpers.specTreeData, (newData) => {
  }, { deep: true });
});

/**
 */
async function buildSpecTreeFromProvidedCostData(costData) {
  if (!Array.isArray(costData) || costData.length === 0) {
    return false;
  }
  
  try {
    
    // 创建基础树结构
    const powderNode = {
      id: 'powder',
      label: '粉剂',
      value: 'powder',
      children: [
        {
          id: 'humic',
          label: '腐植酸类',
          value: '1',  // subType=1表示腐植酸
          parentValue: 'powder',
          children: []
        },
        {
          id: 'nonHumic',
          label: '非腐植酸类',
          value: '0',  // subType=0表示非腐植酸
          parentValue: 'powder',
          children: []
        }
      ]
    };
    
    const liquidNode = {
      id: 'liquid',
      label: '液体',
      value: 'liquid',
      children: []
    };
    
    // 用于跟踪已处理的规格，避免重复
    const processedSpecs = new Set();
    
    // 按类型和子类型分组处理数据
    costData.forEach(item => {
      // 创建唯一标识，避免重复添加相同的规格
      const specKey = `${item.type}-${item.subType}-${item.spec}`;
      
      if (processedSpecs.has(specKey)) {
        return; // 跳过已处理的规格
      }
      
      processedSpecs.add(specKey);
      
      // 格式化规格标签
      const formatSpecLabel = (spec) => {
        if (!spec) return "";
        
        // 将下划线替换为空格
        let label = spec.replace(/_/g, ' ');
        
        // 规格美化
        if (label.includes('bag')) {
          label = label.replace('bag', '袋装');
        }
        if (label.includes('bulk')) {
          label = label.replace('bulk', '散装');
        }
        
        return label;
      };
      
      const specLabel = formatSpecLabel(item.spec);
      const nodeValue = `${item.type}-${item.subType}-${item.spec}`;
      
      if (item.type === 'powder') {
        // 粉剂类型
        const targetSubType = item.subType === '1' || item.subType === 1 
          ? powderNode.children[0] 
          : powderNode.children[1];
        
        targetSubType.children.push({
          id: nodeValue,
          label: specLabel,
          value: nodeValue,
          parentValue: String(item.subType),
        });
      } else if (item.type === 'liquid') {
        // 液体类型
        liquidNode.children.push({
          id: nodeValue,
          label: specLabel, 
          value: nodeValue,
          parentValue: 'liquid',
        });
      }
    });
    
    // 设置树形数据
    if (specHelpers && specHelpers.specTreeData) {
      specHelpers.specTreeData.value = [powderNode, liquidNode];
      
      // 同时更新当前组件中的数据
      if (specTreeData) {
        specTreeData.value = specHelpers.specTreeData.value;
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    return false;
  }
}

const changeProducer = (selectedId) => {
  // 从companyList中查找匹配的option
  let selectedOption = null
  if(companyList.value.length > 0){
    selectedOption = companyList.value.find(item => item.id == selectedId);
  }
  if (selectedOption) {
    producerId.value = selectedOption.id;
    producerName.value = selectedOption.name;
  } else {
    producerId.value = null;
    producerName.value = null;
  }
};
</script>

<style lang="scss" scoped>
.selected-type-display {
  margin-top: 5px;
  font-size: 12px;
  color: #606266;
}

/* 配方弹窗样式优化 */
:deep(.formula-dialog .el-dialog__header) {
  position: relative;
  padding: 12px 20px;
  margin-right: 0;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
}

:deep(.formula-dialog .el-dialog__header::after) {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--el-color-primary);
  content: "";
}

:deep(.formula-dialog .el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

:deep(.formula-dialog .el-dialog__body) {
  padding: 15px 20px;
  color: #333;
  background-color: #fff;
}

:deep(.formula-dialog .el-dialog__footer) {
  padding: 12px 20px;
  background-color: #fff;
  border-top: 1px solid #ebeef5;
}

/* 基本信息卡片样式 */
.basic-info-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.basic-info-card .card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.basic-info-card .card-header i {
  margin-right: 8px;
  color: var(--el-color-primary);
}

/* 详情卡片样式 */
.detail-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-card .card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.detail-card .card-header i {
  margin-right: 8px;
  color: var(--el-color-primary);
}

/* 描述列表样式 */
.info-descriptions {
  margin: 0;
}

:deep(.info-descriptions .el-descriptions__label) {
  font-weight: 600;
  color: #606266;
  background-color: #fafafa;
}

:deep(.info-descriptions .el-descriptions__content) {
  color: #303133;
}

/* NPK标签样式 */
.npk-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.npk-tag {
  margin: 0;
  font-weight: 500;
}

/* 元素标签样式 */
.element-tag {
  margin: 2px 4px 2px 0;
  font-size: 12px;
}

.element-name {
  font-weight: 500;
}

.element-value {
  font-weight: 600;
}

/* 表格容器样式 */
.adaptive-layout-container {
  width: 100%;
}

.table-section {
  width: 100%;
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

/* 表格样式 */
.detail-table {
  font-size: 14px;
}

:deep(.detail-table .el-table__header-wrapper) {
  background-color: #f5f7fa;
}

:deep(.detail-table .el-table__header th) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: bold;
  border-bottom: 1px solid #ebeef5;
}

:deep(.detail-table .el-table__body tr:hover > td) {
  background-color: #f5f7fa;
}

/* 格式化文本样式 */
.formatted-text {
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 空值样式 */
.empty-value {
  color: #c0c4cc;
  font-style: italic;
}

/* 重要值样式 */
.important-value {
  font-weight: 600;
  color: #303133;
}

:global(.spec-tree-select .el-tree-node .el-select-dropdown__item) {
  height: 100% !important;
  line-height: 32px !important;
  width: 350px !important;
}

// 行内表单样式优化
:deep(.el-form--inline) {
  .el-form-item {
    margin-right: 20px;
    margin-bottom: 15px;

    // 确保表单项标签和内容对齐
    .el-form-item__label {
      white-space: nowrap;
    }

    // 产品规格选择器样式
    .spec-tree-select {
      min-width: 350px;
    }
  }
}

.app-container {
  padding: 20px;
}

.icon-pad {
  padding-right: 5px;
}

.clear-fix::after {
  display: block;
  clear: both;
}

.app-tab-group {
  margin-top: 10px;
}

.top-actions {
  margin-bottom: 20px;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-left {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.search-right {
  display: flex;
  gap: 10px;
}

.detail-table {
  width: 100%;
  margin-bottom: 20px;
  border: 1px solid #dcdfe6;
  border-collapse: collapse;

  ::v-deep(th) {
    padding: 8px 5px !important;
    font-size: 13px !important;
    font-weight: bold !important;
    color: #333 !important;
    text-align: center !important;
    background-color: #fff !important;
    border-bottom: 1px solid #dcdfe6 !important;
  }

  ::v-deep(td) {
    height: auto !important;
    padding: 6px 5px !important;
    font-size: 13px !important;
    line-height: 1.3 !important;
    text-align: center !important;
    background-color: #fff !important;
  }

  ::v-deep(.el-table__row) {
    height: auto !important;
  }

  ::v-deep(.el-table__row:nth-child(even)) {
    background-color: #fff !important;
  }

  ::v-deep(.el-table__footer) {
    font-weight: bold;
    background-color: #fff !important;
  }

  ::v-deep(.el-table__footer-wrapper td) {
    padding: 6px 5px !important;
    font-size: 13px !important;
    font-weight: bold;
    color: #333;
    background-color: #fff !important;
  }
  
  ::v-deep(.el-table__inner-wrapper) {
    overflow-x: auto;
  }
  
  ::v-deep(.cell) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.element-summary-table {
  width: 100%;
  margin-bottom: 20px;
  border-collapse: collapse;

  ::v-deep(th) {
    padding: 8px 5px !important;
    font-size: 13px !important;
    font-weight: bold !important;
    color: #333 !important;
    text-align: center !important;
    background-color: #fff !important;
    border-bottom: 1px solid #dcdfe6 !important;
  }

  ::v-deep(td) {
    padding: 6px 5px !important;
    font-size: 13px !important;
    font-weight: bold;
    text-align: center !important;
    background-color: #fff !important;
  }
}

.card-header {
  display: flex;
  margin-bottom: 8px !important;
  font-size: 15px;
  font-weight: bold;
  align-items: center;

  i {
    margin-right: 6px;
    color: #409eff;
  }
}

.divider-title {
  font-size: 15px;
  font-weight: bold;
}

.stats-row {
  margin-top: 15px;
  margin-bottom: 10px;
}

.stat-card {
  padding: 5px;
  margin-bottom: 5px;
}

.stat-unit {
  margin-left: 4px;
  font-size: 12px;
  color: #606266;
}

::v-deep(.el-dialog) {
  .el-dialog__body {
    padding: 15px 20px !important;
  }

  .el-dialog__header {
    padding: 12px 15px !important;
    margin-bottom: 0 !important;
    border-bottom: 1px solid #dcdfe6;

    .el-dialog__title {
      font-size: 16px !important;
      font-weight: bold !important;
    }
  }
}

::v-deep(.formula-descriptions) {
  .el-descriptions__label {
    padding: 8px 6px !important;
    font-size: 13px !important;
    font-weight: bold !important;
    color: #333 !important;
    background-color: #fff !important;
  }

  .el-descriptions__content {
    padding: 8px 6px !important;
    font-size: 13px !important;
    background-color: #fff !important;
  }

  .el-descriptions__table {
    border: 1px solid #dcdfe6 !important;
  }

  .el-descriptions-item__label.is-bordered-label {
    background-color: #fff !important;
  }

  .el-descriptions-item__content.is-bordered-content {
    background-color: #fff !important;
  }
}

.element-tag,
.npk-tag,
.cost-tag {
  padding: 2px 4px !important;
  margin: 2px;
}

.element-name,
.element-value,
.element-summary-name,
.element-summary-value {
  font-size: 12px !important;
}

.important-value {
  font-size: 14px !important;
  font-weight: bold;
  color: #333;
}

.empty-value {
  font-size: 12px !important;
  color: #909399;
}

.cost-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  align-items: center;
}

.tech-specs {
  .tech-spec-item {
    margin-top: 6px;
    font-size: 13px;
  }

  .tech-spec-label {
    margin-right: 6px;
    font-weight: bold;
  }

  .tech-spec-value {
    color: #333;
  }
}

::v-deep(.el-tabs) {
  .el-tabs__header {
    margin-bottom: 15px !important;
  }

  .el-tabs__nav {
    border-radius: 4px !important;
  }

  .el-tabs__item {
    height: 36px !important;
    font-size: 14px !important;
    line-height: 36px !important;
  }

  .el-tabs__item.is-active {
    font-weight: bold !important;
  }
}

.elements-summary-card {
  display: flex;
  padding: 8px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  flex-wrap: wrap;
  gap: 6px;
}

.formatted-text {
  max-height: 100px;
  min-height: auto;
  font-size: 13px;
  line-height: 1.4;
  color: #333;
  white-space: pre-wrap;
}

.npk-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.detail-card {
  height: auto;
  overflow: visible;
}

:deep(.detail-card .el-card__body) {
  padding: 5px;
}

.dialog-footer {
  margin-top: 15px;
}

.elements-table {
  margin-bottom: 10px;
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 8%);
}

:deep(.elements-table .el-table__row) {
  height: 30px !important;
}

:deep(.elements-table .cell) {
  padding: 6px;
}

.element-value {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
}

.element-summary-item {
  color: #fff;
}

.add-company{
  width: 100%;
  margin: 0;
  line-height: 40px;
  color: rgb(4 180 250);
  text-align: center;
  cursor: pointer;
}

/* 改进表格自适应能力 */
:deep(.el-table) {
  height: auto !important;
  max-height: none;
}

:deep(.el-table__body-wrapper) {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

/* 减小分隔区域 */
:deep(.el-divider--horizontal) {
  margin: 8px 0;
}

:deep(.el-divider__text) {
  padding: 0 10px;
  font-size: 14px;
}

/* 减小描述列表的占用空间 */
:deep(.el-descriptions__body .el-descriptions__table) {
  padding: 0;
  margin: 0;
}

:deep(.el-descriptions__cell) {
  padding: 5px !important;
}

:deep(.el-descriptions-item__label) {
  padding: 5px !important;
  font-size: 13px !important;
}

:deep(.el-descriptions-item__content) {
  padding: 5px !important;
  font-size: 13px !important;
}

/* 优化标签空间 */
.element-tag,
.npk-tag,
.cost-tag {
  padding: 2px 4px !important;
  margin: 2px;
}

/* 基本信息卡片保持原样 */
.basic-info-card {
  margin-bottom: 20px;
}

:deep(.basic-info-card .el-card__body) {
  padding: 15px 20px;
}

/* 自适应卡片容器 - 只针对详情卡片 */
.detail-card {
  width: fit-content;
  max-width: 100%;
  height: auto;
  overflow: visible;
  display: inline-block;
  vertical-align: top;
  margin-right: 20px;
  margin-bottom: 20px;
}

:deep(.detail-card .el-card__body) {
  padding: 5px;
}

/* 元素含量区域紧凑化 */
.formatted-text {
  max-height: 100px;
  min-height: auto;
}

/* 调整表格字体大小 */
:deep(.el-table th) {
  font-size: 13px !important;
}

:deep(.el-table td) {
  font-size: 13px !important;
}

:deep(.el-table .cell) {
  font-size: 13px !important;
}

/* 调整描述列表字体 */
:deep(.el-descriptions-item__label) {
  font-size: 13px !important;
}

:deep(.el-descriptions-item__content) {
  font-size: 13px !important;
}

/* 调整统计卡片字体 */
:deep(.el-statistic__title) {
  font-size: 13px !important;
}

:deep(.el-statistic__content) {
  font-size: 15px !important;
}

/* 调整元素标签字体 */
.element-tag,
.npk-tag,
.cost-tag {
  font-size: 13px !important;
}

.element-name,
.element-value {
  font-size: 13px !important;
}

/* 分隔线文字 */
:deep(.el-divider__text) {
  font-size: 14px;
}

/* 文本区域字体 */
.formatted-text {
  font-size: 13px;
}

/* 表格水平滚动容器 */
.table-container {
  width: 100%;
  padding-bottom: 5px;
  overflow-x: auto;
}

/* 确保固定列样式正确 */
:deep(.el-table .el-table__fixed) {
  height: auto !important;
  box-shadow: 0 0 10px rgb(0 0 0 / 10%);
}

:deep(.el-table .el-table__fixed-right) {
  height: auto !important;
  box-shadow: -3px 0 10px rgb(0 0 0 / 10%);
}

/* 优化表格在横向滚动时的样式 */
:deep(.detail-table) {
  width: auto;
  min-width: 100%;
}



/* 确保合计行样式正确 */
:deep(.el-table__footer-wrapper) {
  z-index: 1;
}

/* 合计行上边框 */
:deep(.el-table__footer-wrapper) tr td {
  font-weight: bold;
  background-color: #F8F8F9;
  border-top: 1px solid #EBEEF5 !important;
}

/* 详情布局容器 */
.detail-layout-container {
  display: flex;
  gap: 20px;
  align-items: stretch;
  width: 100%;
}

/* 表格卡片 - 自适应宽度 */
.detail-card {
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */
  overflow: visible; /* 确保表格内容不被裁剪 */
  display: flex;
  flex-direction: column;
}

/* 窄表格卡片 - 当表格较窄时，让表格占据合适的空间 */
.narrow-table-card {
  flex: 0 0 auto; /* 不伸缩，保持内容宽度 */
  width: auto;
  min-width: fit-content;
  display: flex;
  flex-direction: column;
}

/* 右侧汇总卡片 - 占据剩余空间 */
.right-summary-card {
  flex: 1; /* 占据剩余空间 */
  min-width: 300px; /* 最小宽度300px */
  display: flex;
  flex-direction: column;
  align-self: stretch; /* 确保与左侧卡片高度一致 */
}

/* 右侧汇总卡片内的el-card */
.right-summary-card .summary-info-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  margin: 0; /* 移除默认margin */
  min-height: 100%; /* 确保至少与左侧卡片同高 */
}

/* 确保el-card组件内部也能正确拉伸 */
.right-summary-card :deep(.el-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0; /* 移除默认margin */
  min-height: 100%;
}

.right-summary-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 内容从顶部开始排列 */
  min-height: 0; /* 允许内容区域收缩 */
  gap: 0; /* 移除默认间距，使用自定义间距 */
}

/* 汇总信息卡片的主要内容区域布局 */
.right-summary-card .summary-info-card :deep(.el-card__body) > .el-divider:first-child {
  margin-top: 0; /* 第一个分割线顶部无间距 */
}

.right-summary-card .summary-info-card :deep(.el-card__body) > .stats-row:first-of-type {
  margin-bottom: 16px; /* 统计行底部间距 */
}

/* 当有元素含量汇总时，为其分配更多空间 */
.right-summary-card .summary-info-card :deep(.el-card__body) > div:last-child {
  flex: 1 1 auto; /* 最后一个div（元素含量汇总区域）占据剩余空间 */
  display: flex;
  flex-direction: column;
}

/* 确保表格卡片也能正确拉伸 */
.detail-card :deep(.el-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0; /* 移除默认margin */
}

.detail-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 确保卡片对齐 */
.detail-layout-container > .detail-card,
.detail-layout-container > .right-summary-card {
  margin: 0;
  align-self: stretch;
}



/* 表格容器样式 - 确保表格不需要滚动 */
.table-container {
  width: 100%;
  overflow: visible; /* 移除滚动，让表格自然展开 */
}

.adaptive-layout-container {
  width: 100%;
  overflow: visible;
}

/* 表格样式优化 */
.detail-table {
  width: 100% !important;
  min-width: fit-content;
}

/* 确保表格列宽度自适应 */
:deep(.detail-table .el-table__header-wrapper),
:deep(.detail-table .el-table__body-wrapper) {
  overflow: visible !important;
}

/* 包装明细表格固定宽度 */
:deep(.package-table) {
  width: 660px !important;
  max-width: 660px !important;
  min-width: 660px !important;
}

:deep(.package-table .el-table__inner-wrapper),
:deep(.package-table .el-table__header-wrapper),
:deep(.package-table .el-table__body-wrapper) {
  width: 660px !important;
  max-width: 660px !important;
  overflow: hidden !important;
}

/* 包装明细卡片固定宽度 */
.package-card {
  flex: 0 0 680px !important;
  width: 680px !important;
  max-width: 680px !important;
  min-width: 680px !important;
}

/* 包装明细表格容器固定宽度 */
.package-card .table-container {
  width: 660px !important;
  max-width: 660px !important;
  overflow-x: hidden !important;
}

.package-card .adaptive-layout-container {
  width: 660px !important;
  max-width: 660px !important;
}

/* 右侧汇总样式 - 与左侧卡片保持一致 */
.right-summary-style {
  background: #fff !important;
  border: 1px solid #ebeef5 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
}

/* 右侧汇总卡片的统计样式 */
.right-summary-card .stat-card {
  padding: 10px 12px;
  margin-bottom: 10px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  transition: all 0.2s ease;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 0 0 auto; /* 不允许统计卡片拉伸 */
}

.right-summary-card .stat-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

/* 当表格很高时，增加统计卡片的间距 */
.right-summary-card .stats-row {
  flex: 0 0 auto;
  margin-bottom: 20px;
}

.right-summary-card :deep(.el-statistic) {
  text-align: center;
}

.right-summary-card :deep(.el-statistic__title) {
  font-size: 12px;
  margin-bottom: 4px;
  color: #606266;
  font-weight: 500;
  line-height: 1.2;
}

.right-summary-card :deep(.el-statistic__content) {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.right-summary-card :deep(.el-statistic__suffix) {
  font-size: 11px;
  color: #909399;
  margin-left: 2px;
}

/* 元素含量汇总区域间距和布局 */
.right-summary-card .el-divider {
  margin: 24px 0 16px 0;
  flex: 0 0 auto;
}

.right-summary-card .elements-table {
  margin-top: 16px;
  flex: 1 1 auto; /* 允许表格区域拉伸以填充剩余空间 */
  min-height: 0;
}

/* 元素含量汇总容器 */
.right-summary-card .stats-row:has(.elements-table) {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
}

/* 当表格很高时，为元素含量汇总区域添加更多空间 */
.right-summary-card .elements-table :deep(.el-table) {
  min-height: 120px; /* 设置最小高度 */
}

.right-summary-card .elements-table :deep(.el-table__body-wrapper) {
  min-height: 80px;
}

/* 响应式布局 */
@media (max-width: 1400px) {
  .detail-layout-container {
    flex-direction: column;
  }

  .right-summary-card {
    flex: none;
    width: 100%;
    position: static;
    max-height: none;
    margin-top: 20px;
  }

  .narrow-table-card {
    width: 100%;
    max-width: none;
  }

  /* 在中等屏幕上显示底部汇总 */
  .bottom-summary {
    display: block !important;
  }
}

@media (max-width: 1200px) {
  .detail-layout-container {
    flex-direction: column;
  }

  .right-summary-card {
    flex: none;
    width: 100%;
    min-width: auto;
    margin-top: 20px;
  }

  .narrow-table-card {
    width: 100%;
    max-width: none;
  }

  /* 包装明细在小屏幕上的特殊处理 */
  .package-card {
    width: 100% !important;
    max-width: none !important;
    min-width: auto !important;
    flex: none !important;
  }

  .package-card .table-container,
  .package-card .adaptive-layout-container {
    width: 100% !important;
    max-width: none !important;
    overflow-x: auto !important;
  }

  .package-card :deep(.package-table),
  .package-card :deep(.package-table .el-table__inner-wrapper),
  .package-card :deep(.package-table .el-table__header-wrapper),
  .package-card :deep(.package-table .el-table__body-wrapper) {
    width: 660px !important; /* 保持表格最小宽度，允许水平滚动 */
    min-width: 660px !important;
  }
}

/* 中等屏幕 - 保持水平布局但调整最小宽度 */
@media (min-width: 1201px) and (max-width: 1400px) {
  .right-summary-card {
    flex: 1;
    min-width: 280px;
  }
}

/* 大屏幕时的优化 */
@media (min-width: 1401px) {
  .detail-layout-container {
    align-items: stretch;
    gap: 24px;
  }

  .right-summary-card {
    flex: 1;
    min-width: 320px;
  }
}

/* 预览弹窗样式 */
.preview-container {
  max-height: 70vh;
  overflow-y: auto;
}

.preview-container .info-descriptions {
  margin-bottom: 16px;
}

.preview-container .compact-descriptions {
  font-size: 13px;
}

.preview-container .ultra-compact {
  margin-bottom: 12px;
}

.preview-container .ultra-compact-text {
  font-size: 12px;
  line-height: 1.4;
  max-height: 60px;
  overflow-y: auto;
}

.preview-container .important-value {
  font-weight: 600;
  color: #303133;
}

.preview-container .empty-value {
  color: #909399;
  font-style: italic;
}

.preview-container .npk-text-container {
  font-size: 12px;
  line-height: 1.4;
}

.preview-container .formatted-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.preview-container .el-table {
  font-size: 12px;
}

/* 预览弹窗增强样式 */
.preview-container .el-divider {
  margin: 24px 0 16px 0;
  font-weight: 600;
}

.preview-container .amount-value,
.preview-container .price-value {
  font-weight: 500;
}

/* NPK 展示样式 */
.preview-container .npk-preview-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.preview-container .npk-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.preview-container .npk-tag {
  margin-right: 8px;
}

/* 元素展示样式 */
.preview-container .elements-preview-container {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

.preview-container .element-tag {
  margin-right: 6px;
  margin-bottom: 4px;
  font-size: 12px;
}

.preview-container .el-table th {
  padding: 8px 0;
}

.preview-container .el-table td {
  padding: 6px 0;
}

.dialog-footer .submit-button {
  background-color: #409eff;
  border-color: #409eff;
}

.dialog-footer .cancel-button {
  background-color: #f56c6c;
  border-color: #f56c6c;
  color: white;
}
</style>
